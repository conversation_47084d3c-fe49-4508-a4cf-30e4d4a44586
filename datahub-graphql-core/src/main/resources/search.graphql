extend type Query {
  """
  Full text search against a specific DataHub Entity Type
  """
  search(input: SearchInput!): SearchResults

  """
  Search DataHub entities
  """
  searchAcrossEntities(input: SearchAcrossEntitiesInput!): SearchResults

  """
  Search DataHub entities by providing a pointer reference for scrolling through results.
  """
  scrollAcrossEntities(input: ScrollAcrossEntitiesInput!): ScrollResults

  """
  Search across the results of a graph query on a node
  """
  searchAcrossLineage(
    input: SearchAcrossLineageInput!
  ): SearchAcrossLineageResults

  """
  Search across the results of a graph query on a node, uses scroll API
  """
  scrollAcrossLineage(
    input: ScrollAcrossLineageInput!
  ): ScrollAcrossLineageResults

  """
  Autocomplete a search query against a specific DataHub Entity Type
  """
  autoComplete(input: AutoCompleteInput!): AutoCompleteResults

  """
  Autocomplete a search query against a specific set of DataHub Entity Types
  """
  autoCompleteForMultiple(
    input: AutoCompleteMultipleInput!
  ): AutoCompleteMultipleResults

  """
  Hierarchically browse a specific type of DataHub Entity by path
  Used by explore in the UI
  """
  browse(input: BrowseInput!): BrowseResults

  """
  Retrieve the browse paths corresponding to an entity
  """
  browsePaths(input: BrowsePathsInput!): [BrowsePath!]

  """
  Aggregate across DataHub entities
  """
  aggregateAcrossEntities(
    input: AggregateAcrossEntitiesInput!
  ): AggregateResults

  """
  List Data Product assets for a given urn
  """
  listDataProductAssets(
    urn: String!
    input: SearchAcrossEntitiesInput!
  ): SearchResults

  """
  List Application assets for a given urn
  """
  listApplicationAssets(
    urn: String!
    input: SearchAcrossEntitiesInput!
  ): SearchResults

  """
  Browse for different entities by getting organizational groups and their
  aggregated counts + content. Uses browsePathsV2 aspect and replaces our old
  browse endpoint.
  """
  browseV2(input: BrowseV2Input!): BrowseResultsV2
}

"""
Input arguments for a full text search query
"""
input SearchInput {
  """
  The Metadata Entity type to be searched against
  """
  type: EntityType!

  """
  The raw query string
  """
  query: String!

  """
  The offset of the result set
  """
  start: Int

  """
  The number of entities to include in result set
  """
  count: Int

  """
  Deprecated in favor of the more expressive orFilters field
  Facet filters to apply to search results. These will be 'AND'-ed together.
  """
  filters: [FacetFilterInput!]
    @deprecated(reason: "Use `orFilters`- they are more expressive")

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]

  """
  Flags controlling search options
  """
  searchFlags: SearchFlags
}

"""
Set of flags to control search behavior
"""
input SearchFlags {
  """
  Whether to skip cache
  """
  skipCache: Boolean

  """
  The maximum number of values in an facet aggregation
  """
  maxAggValues: Int

  """
  Structured or unstructured fulltext query
  """
  fulltext: Boolean

  """
  Whether to skip highlighting
  """
  skipHighlighting: Boolean

  """
  Whether to skip aggregates/facets
  """
  skipAggregates: Boolean

  """
  Whether to request for search suggestions on the _entityName virtualized field
  """
  getSuggestions: Boolean

  """
  Additional grouping specifications to apply to the search results
  Grouping specifications will control how search results are grouped together
  in the response. This is currently being used to group schema fields (columns)
  as datasets, and in the future will be used to group other entities as well.
  Note: This is an experimental feature and is subject to change.
  """
  groupingSpec: GroupingSpec

  """
  Whether to include soft deleted entities
  """
  includeSoftDeleted: Boolean

  """
  Whether to include restricted entities
  """
  includeRestricted: Boolean

  """
  fields to include for custom Highlighting
  """
  customHighlightingFields: [String!]

  """
  Whether or not to fetch and request for structured property facets when doing a search
  """
  includeStructuredPropertyFacets: Boolean

  """
  Determines whether to filter out any non-latest entity version if entity is part of a Version Set, default true
  """
  filterNonLatestVersions: Boolean
}

"""
Flags to control lineage behavior
"""
input LineageFlags {
  """
  Limits the number of results explored per hop, still gets all edges each time a hop happens
  """
  entitiesExploredPerHopLimit: Int

  """
  An optional starting time to filter on
  """
  startTimeMillis: Long
  """
  An optional ending time to filter on
  """
  endTimeMillis: Long

  """
  Map of entity types to platforms to ignore when counting hops during graph walk. Note: this can potentially cause
  a large amount of additional hops to occur and should be used with caution.
  """
  ignoreAsHops: [EntityTypeToPlatforms!]
}

input EntityTypeToPlatforms {
  """
  Entity type to ignore as hops, if no platform is applied applies to all entities of this type.
  """
  entityType: EntityType!

  """
  List of platforms to ignore as hops, empty implies all. Must be a valid platform urn
  """
  platforms: [String!]
}

"""
Input arguments for a full text search query across entities
"""
input SearchAcrossEntitiesInput {
  """
  Entity types to be searched. If this is not provided, all entities will be searched.
  """
  types: [EntityType!]

  """
  The query string
  """
  query: String!

  """
  The starting point of paginated results
  """
  start: Int

  """
  The number of elements included in the results
  """
  count: Int

  """
  Deprecated in favor of the more expressive orFilters field
  Facet filters to apply to search results. These will be 'AND'-ed together.
  """
  filters: [FacetFilterInput!]
    @deprecated(reason: "Use `orFilters`- they are more expressive")

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]

  """
  Optional - A View to apply when generating results
  """
  viewUrn: String

  """
  Flags controlling search options
  """
  searchFlags: SearchFlags

  """
  Optional - Information on how to sort this search result
  """
  sortInput: SearchSortInput
}

"""
Input arguments for a full text search query across entities to get aggregations
"""
input AggregateAcrossEntitiesInput {
  """
  Entity types to be searched. If this is not provided, all entities will be searched.
  """
  types: [EntityType!]

  """
  The query string
  """
  query: String!

  """
  The list of facets to get aggregations for. If list is empty or null, get aggregations for all facets
  Sub-aggregations can be specified with the unicode character ␞ (U+241E) as a delimiter between the subtypes.
  e.g. _entityType␞owners
  """
  facets: [String]

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]

  """
  Optional - A View to apply when generating results
  """
  viewUrn: String

  """
  Flags controlling search options
  """
  searchFlags: SearchFlags
}

"""
Input arguments for a full text search query across entities, specifying a starting pointer. Allows paging beyond 10k results
"""
input ScrollAcrossEntitiesInput {
  """
  Entity types to be searched. If this is not provided, all entities will be searched.
  """
  types: [EntityType!]

  """
  The query string
  """
  query: String!

  """
  The starting point of paginated results, an opaque ID the backend understands as a pointer
  """
  scrollId: String

  """
  The amount of time to keep the point in time snapshot alive, takes a time unit based string ex: 5m or 30s
  """
  keepAlive: String

  """
  The number of elements included in the results
  """
  count: Int

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]

  """
  Optional - A View to apply when generating results
  """
  viewUrn: String

  """
  Flags controlling search options
  """
  searchFlags: SearchFlags

  """
  Optional - Information on how to sort this search result
  """
  sortInput: SearchSortInput
}

"""
Input arguments for a search query over the results of a multi-hop graph query
"""
input SearchAcrossLineageInput {
  """
  Urn of the source node
  """
  urn: String

  """
  The direction of the relationship, either incoming or outgoing from the source entity
  """
  direction: LineageDirection!

  """
  Entity types to be searched. If this is not provided, all entities will be searched.
  """
  types: [EntityType!]

  """
  The query string
  """
  query: String

  """
  The starting point of paginated results
  """
  start: Int

  """
  The number of elements included in the results
  """
  count: Int

  """
  Deprecated in favor of the more expressive orFilters field
  Facet filters to apply to search results. These will be 'AND'-ed together.
  """
  filters: [FacetFilterInput!]
    @deprecated(reason: "Use `orFilters`- they are more expressive")

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]
  """
  An optional starting time to filter on
  """
  startTimeMillis: Long @deprecated(reason: "Use LineageFlags instead")
  """
  An optional ending time to filter on
  """
  endTimeMillis: Long @deprecated(reason: "Use LineageFlags instead")

  """
  Flags controlling search options
  """
  searchFlags: SearchFlags

  """
  Flags controlling the lineage query
  """
  lineageFlags: LineageFlags

  """
  Optional - A View to apply when generating results
  """
  viewUrn: String

  """
  Optional - Information on how to sort this search result
  """
  sortInput: SearchSortInput
}

"""
Input arguments for a search query over the results of a multi-hop graph query, uses scroll API
"""
input ScrollAcrossLineageInput {
  """
  Urn of the source node
  """
  urn: String

  """
  The direction of the relationship, either incoming or outgoing from the source entity
  """
  direction: LineageDirection!

  """
  Entity types to be searched. If this is not provided, all entities will be searched.
  """
  types: [EntityType!]

  """
  The query string
  """
  query: String

  """
  The starting point of paginated results, an opaque ID the backend understands as a pointer
  """
  scrollId: String

  """
  The amount of time to keep the point in time snapshot alive, takes a time unit based string ex: 5m or 30s
  """
  keepAlive: String

  """
  The number of elements included in the results
  """
  count: Int

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]

  """
  An optional starting time to filter on
  """
  startTimeMillis: Long

  """
  An optional ending time to filter on
  """
  endTimeMillis: Long

  """
  Flags controlling search options
  """
  searchFlags: SearchFlags

  """
  Flags controlling the lineage query
  """
  lineageFlags: LineageFlags
}

"""
A list of disjunctive criterion for the filter. (or operation to combine filters)
"""
input AndFilterInput {
  """
  A list of and criteria the filter applies to the query
  """
  and: [FacetFilterInput!]
}

"""
Facet filters to apply to search results
"""
input FacetFilterInput {
  """
  Name of field to filter by
  """
  field: String!

  """
  Value of the field to filter by. Deprecated in favor of `values`, which should accept a single element array for a
  value
  """
  value: String @deprecated(reason: "Prefer `values` for single elements")

  """
  Values, one of which the intended field should match.
  """
  values: [String!]

  """
  If the filter should or should not be matched
  """
  negated: Boolean

  """
  Condition for the values. How to If unset, assumed to be equality
  """
  condition: FilterOperator
}

enum FilterOperator {
  """
  Represent the relation: String field contains value, e.g. name contains Profile
  """
  CONTAIN

  """
  Represent the relation: field = value, e.g. platform = hdfs
  """
  EQUAL

  """
  Represent the relation: field = value (case-insensitive), e.g. platform = HDFS
  """
  IEQUAL

  """
  * Represent the relation: String field is one of the array values to, e.g. name in ["Profile", "Event"]
  """
  IN

  """
  Represents the relation: The field exists. If the field is an array, the field is either not present or empty.
  """
  EXISTS

  """
  Represent the relation greater than, e.g. ownerCount > 5
  """
  GREATER_THAN

  """
  Represent the relation greater than or equal to, e.g. ownerCount >= 5
  """
  GREATER_THAN_OR_EQUAL_TO

  """
  Represent the relation less than, e.g. ownerCount < 3
  """
  LESS_THAN

  """
  Represent the relation less than or equal to, e.g. ownerCount <= 3
  """
  LESS_THAN_OR_EQUAL_TO

  """
  Represent the relation: String field starts with value, e.g. name starts with PageView
  """
  START_WITH

  """
  Represent the relation: String field ends with value, e.g. name ends with Event
  """
  END_WITH

  """
  Represent the relation: URN field any nested children in addition to the given URN
  """
  DESCENDANTS_INCL

  """
  Represent the relation: URN field matches any nested parent in addition to the given URN
  """
  ANCESTORS_INCL

  """
  Represent the relation: URN field matches any nested child or parent in addition to the given URN
  """
  RELATED_INCL
}

"""
Results returned by issuing a search query
"""
type SearchResults {
  """
  The offset of the result set
  """
  start: Int!

  """
  The number of entities included in the result set
  """
  count: Int!

  """
  The total number of search results matching the query and filters
  """
  total: Int!

  """
  The search result entities
  """
  searchResults: [SearchResult!]!

  """
  Candidate facet aggregations used for search filtering
  """
  facets: [FacetMetadata!]

  """
  Search suggestions based on the query provided for alternate query texts
  """
  suggestions: [SearchSuggestion!]
}

type ExtraProperty {
  """
  Name of the extra property
  """
  name: String!

  """
  Value of the extra property
  """
  value: String!
}

"""
An individual search result hit
"""
type SearchResult {
  """
  The resolved DataHub Metadata Entity matching the search query
  """
  entity: Entity!

  """
  Insights about why the search result was matched
  """
  insights: [SearchInsight!]

  """
  Matched field hint
  """
  matchedFields: [MatchedField!]!

  """
  Additional properties about the search result. Used for rendering in the UI
  """
  extraProperties: [ExtraProperty!]
}

"""
Results returned from aggregateAcrossEntities
"""
type AggregateResults {
  """
  Candidate facet aggregations used for search filtering
  """
  facets: [FacetMetadata!]
}

"""
Results returned by issuing a search query
"""
type ScrollResults {
  """
  Opaque ID to pass to the next request to the server
  """
  nextScrollId: String

  """
  The number of entities included in the result set
  """
  count: Int!

  """
  The total number of search results matching the query and filters
  """
  total: Int!

  """
  The search result entities for a scroll request
  """
  searchResults: [SearchResult!]!

  """
  Candidate facet aggregations used for search filtering
  """
  facets: [FacetMetadata!]
}

"""
Results returned by issuing a search across relationships query
"""
type SearchAcrossLineageResults {
  """
  The offset of the result set
  """
  start: Int!

  """
  The number of entities included in the result set
  """
  count: Int!

  """
  The total number of search results matching the query and filters
  """
  total: Int!

  """
  The search result entities
  """
  searchResults: [SearchAcrossLineageResult!]!

  """
  Candidate facet aggregations used for search filtering
  """
  facets: [FacetMetadata!]

  """
  Optional freshness characteristics of this query (cached, staleness etc.)
  """
  freshness: FreshnessStats

  """
  The path taken when doing search across lineage
  """
  lineageSearchPath: LineageSearchPath
}

"""
Results returned by issuing a search across relationships query using scroll API
"""
type ScrollAcrossLineageResults {
  """
  Opaque ID to pass to the next request to the server
  """
  nextScrollId: String

  """
  The number of entities included in the result set
  """
  count: Int!

  """
  The total number of search results matching the query and filters
  """
  total: Int!

  """
  The search result entities
  """
  searchResults: [SearchAcrossLineageResult!]!

  """
  Candidate facet aggregations used for search filtering
  """
  facets: [FacetMetadata!]
}

"""
Individual search result from a search across relationships query (has added metadata about the path)
"""
type SearchAcrossLineageResult {
  """
  The resolved DataHub Metadata Entity matching the search query
  """
  entity: Entity!

  """
  Insights about why the search result was matched
  """
  insights: [SearchInsight!]

  """
  Matched field hint
  """
  matchedFields: [MatchedField!]!

  """
  Optional list of entities between the source and destination node
  """
  paths: [EntityPath]

  """
  Degree of relationship (number of hops to get to entity)
  """
  degree: Int!

  """
  Degrees of relationship (for entities discoverable at multiple degrees)
  """
  degrees: [Int!]

  """
  Marks whether or not this entity was explored further for lineage
  """
  explored: Boolean!

  """
  Indicates this destination node has additional unexplored child relationships
  """
  truncatedChildren: Boolean!

  """
  Whether this relationship was ignored as a hop
  """
  ignoredAsHop: Boolean!
}

"""
An overview of the field that was matched in the entity search document
"""
type EntityPath {
  """
  Path of entities between source and destination nodes
  """
  path: [Entity]!
}

"""
An overview of the field that was matched in the entity search document
"""
type MatchedField {
  """
  Name of the field that matched
  """
  name: String!

  """
  Value of the field that matched
  """
  value: String!

  """
  Entity if the value is an urn
  """
  entity: Entity
}

"""
Insights about why a search result was returned or ranked in the way that it was
"""
type SearchInsight {
  """
  The insight to display
  """
  text: String!

  """
  An optional emoji to display in front of the text
  """
  icon: String
}

"""
Contains valid fields to filter search results further on
"""
type FacetMetadata {
  """
  Name of a field present in the search entity
  """
  field: String!

  """
  Display name of the field
  """
  displayName: String

  """
  Entity corresponding to the facet
  """
  entity: Entity

  """
  Aggregated search result counts by value of the field
  """
  aggregations: [AggregationMetadata!]!
}

"""
Information about the aggregation that can be used for filtering, included the field value and number of results
"""
type AggregationMetadata {
  """
  A particular value of a facet field
  """
  value: String!

  """
  The number of search results containing the value
  """
  count: Long!

  """
  Entity corresponding to the facet field
  """
  entity: Entity

  """
  Optional display name to show in the UI for this filter value
  """
  displayName: String
}

"""
A suggestion for an alternate search query given an original query compared to all
of the entity names in our search index.
"""
type SearchSuggestion {
  """
  The suggested text based on the provided query text compared to
  the entity name field in the search index.
  """
  text: String!

  """
  The "edit distance" for this suggestion. The closer this number is to 1, the
  closer the suggested text is to the original text. The closer it is to 0, the
  further from the original text it is.
  """
  score: Float

  """
  The number of entities that would match on the name field given the suggested text
  """
  frequency: Int
}

"""
Input for performing an auto completion query against a single Metadata Entity
"""
input AutoCompleteInput {
  """
  Entity type to be autocompleted against
  """
  type: EntityType

  """
  The raw query string
  """
  query: String!

  """
  An optional entity field name to autocomplete on
  """
  field: String

  """
  The maximum number of autocomplete results to be returned
  """
  limit: Int

  """
  Faceted filters applied to autocomplete results
  """
  filters: [FacetFilterInput!]

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]
}

"""
Input for performing an auto completion query against a a set of Metadata Entities
"""
input AutoCompleteMultipleInput {
  """
  Entity types to be autocompleted against
  Optional, if none supplied, all searchable types will be autocompleted against
  """
  types: [EntityType!]

  """
  The raw query string
  """
  query: String!

  """
  An optional field to autocomplete against
  """
  field: String

  """
  The maximum number of autocomplete results
  """
  limit: Int

  """
  Faceted filters applied to autocomplete results
  """
  filters: [FacetFilterInput!]

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]

  """
  Optional - A View to apply when generating results
  """
  viewUrn: String
}

"""
An individual auto complete result specific to an individual Metadata Entity Type
"""
type AutoCompleteResultForEntity {
  """
  Entity type
  """
  type: EntityType!

  """
  The autocompletion results for specified entity type
  """
  suggestions: [String!]!

  """
  A list of entities to render in autocomplete
  """
  entities: [Entity!]!
}

"""
The results returned on a multi entity autocomplete query
"""
type AutoCompleteMultipleResults {
  """
  The raw query string
  """
  query: String!

  """
  The autocompletion suggestions
  """
  suggestions: [AutoCompleteResultForEntity!]!
}

"""
The results returned on a single entity autocomplete query
"""
type AutoCompleteResults {
  """
  The query string
  """
  query: String!

  """
  The autocompletion results
  """
  suggestions: [String!]!

  """
  A list of entities to render in autocomplete
  """
  entities: [Entity!]!
}

"""
Input required for browse queries
"""
input BrowseInput {
  """
  The browse entity type
  """
  type: EntityType!

  """
  The browse path
  """
  path: [String!]

  """
  The starting point of paginated results
  """
  start: Int

  """
  The number of elements included in the results
  """
  count: Int

  """
  Deprecated in favor of the more expressive orFilters field
  Facet filters to apply to search results. These will be 'AND'-ed together.
  """
  filters: [FacetFilterInput!]
    @deprecated(reason: "Use `orFilters`- they are more expressive")

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]
}

"""
The results of a browse path traversal query
"""
type BrowseResults {
  """
  The browse results
  """
  entities: [Entity!]!

  """
  The groups present at the provided browse path
  """
  groups: [BrowseResultGroup!]!

  """
  The starting point of paginated results
  """
  start: Int!

  """
  The number of elements included in the results
  """
  count: Int!

  """
  The total number of browse results under the path with filters applied
  """
  total: Int!

  """
  Metadata containing resulting browse groups
  """
  metadata: BrowseResultMetadata!
}

"""
Metadata about the Browse Paths response
"""
type BrowseResultMetadata {
  """
  The provided path
  """
  path: [String!]!

  """
  The total number of entities under the provided browse path
  """
  totalNumEntities: Long!
}

"""
A group of Entities under a given browse path
"""
type BrowseResultGroup {
  """
  The path name of a group of browse results
  """
  name: String!

  """
  The number of entities within the group
  """
  count: Long!
}

"""
Inputs for fetching the browse paths for a Metadata Entity
"""
input BrowsePathsInput {
  """
  The browse entity type
  """
  type: EntityType!

  """
  The entity urn
  """
  urn: String!
}

"""
A hierarchical entity path
"""
type BrowsePath {
  """
  The components of the browse path
  """
  path: [String!]!
}

"""
A hierarchical entity path V2
"""
type BrowsePathV2 {
  """
  The components of the browse path
  """
  path: [BrowsePathEntry!]!
}

type BrowsePathEntry {
  """
  The path name of a group of browse results
  """
  name: String!

  """
  An optional entity associated with this browse entry. This will usually be a container entity.
  If this entity is not populated, the name must be used.
  """
  entity: Entity
}

"""
A set of filter criteria
"""
input FilterInput {
  """
  A list of conjunctive filters
  """
  and: [FacetFilterInput!]!
}

"""
A single filter value
"""
type FacetFilter {
  """
  Name of field to filter by
  """
  field: String!

  """
  Condition for the values.
  """
  condition: FilterOperator

  """
  Values, one of which the intended field should match.
  """
  values: [String!]!

  """
  If the filter should or should not be matched
  """
  negated: Boolean
}

"""
Input for getting Quick Filters
"""
input GetQuickFiltersInput {
  """
  Optional - A View to apply when generating results
  """
  viewUrn: String
}

"""
The result object when fetching quick filters
"""
type GetQuickFiltersResult {
  """
  The list of quick filters to render in the UI
  """
  quickFilters: [QuickFilter]!
}

"""
A quick filter in search and auto-complete
"""
type QuickFilter {
  """
  Name of field to filter by
  """
  field: String!

  """
  Value to filter on
  """
  value: String!

  """
  Entity that the value maps to if any
  """
  entity: Entity
}

"""
Freshness stats for a query result.
Captures whether the query was served out of a cache, what the staleness was, etc.
"""
type FreshnessStats {
  """
  Whether a cache was used to respond to this query
  """
  cached: Boolean

  """
  The latest timestamp in millis of the system that was used to respond to this query
  In case a cache was consulted, this reflects the freshness of the cache
  In case an index was consulted, this reflects the freshness of the index
  """
  systemFreshness: [SystemFreshness]
}

type SystemFreshness {
  """
  Name of the system
  """
  systemName: String!

  """
  The latest timestamp in millis of the system that was used to respond to this query
  In case a cache was consulted, this reflects the freshness of the cache
  In case an index was consulted, this reflects the freshness of the index
  """
  freshnessMillis: Long!
}

"""
Input required for browse queries
"""
input BrowseV2Input {
  """
  The browse entity type - deprecated use types instead
  """
  type: EntityType

  """
  The browse entity type - deprecated use types instead. If not provided, all types will be used.
  """
  types: [EntityType!]

  """
  The browse path V2 - a list with each entry being part of the browse path V2
  """
  path: [String!]

  """
  The starting point of paginated results
  """
  start: Int

  """
  The number of elements included in the results
  """
  count: Int

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]

  """
  Optional - A View to apply when generating results
  """
  viewUrn: String

  """
  The search query string
  """
  query: String

  """
  Flags controlling search options
  """
  searchFlags: SearchFlags
}

"""
The results of a browse path V2 traversal query
"""
type BrowseResultsV2 {
  """
  The groups present at the provided browse path V2
  """
  groups: [BrowseResultGroupV2!]!

  """
  The starting point of paginated results
  """
  start: Int!

  """
  The number of groups included in the results
  """
  count: Int!

  """
  The total number of browse groups under the path with filters applied
  """
  total: Int!

  """
  Metadata containing resulting browse groups
  """
  metadata: BrowseResultMetadata!
}

"""
A group of Entities under a given browse path
"""
type BrowseResultGroupV2 {
  """
  The path name of a group of browse results
  """
  name: String!

  """
  An optional entity associated with this browse group. This will usually be a container entity.
  If this entity is not populated, the name must be used.
  """
  entity: Entity

  """
  The number of entities within the group
  """
  count: Long!

  """
  Whether or not there are any more groups underneath this group
  """
  hasSubGroups: Boolean!
}

"""
Input required in order to sort search results
"""
input SearchSortInput {
  """
  A criterion to sort search results on
  """
  sortCriterion: SortCriterion @deprecated(reason: "Use sortCriteria instead")

  """
  A list of values to sort search results on
  """
  sortCriteria: [SortCriterion!]
}

"""
Order for sorting
"""
enum SortOrder {
  ASCENDING
  DESCENDING
}

"""
A single sorting criterion for sorting search.
"""
input SortCriterion {
  """
  A field upon which we'll do sorting on.
  """
  field: String!

  """
  The order in which we will be sorting
  """
  sortOrder: SortOrder!
}

"""
A grouping specification for search results.
"""
input GroupingSpec {
  """
  A list of grouping criteria for grouping search results.
  There is no implied order in the grouping criteria.
  """
  groupingCriteria: [GroupingCriterion!]
}

"""
A single grouping criterion for grouping search results
"""
input GroupingCriterion {
  """
  The base entity type that needs to be grouped
  e.g. schemaField
  Omitting this field will result in all base entities being grouped into the groupingEntityType.
  """
  baseEntityType: EntityType

  """
  The type of entity being grouped into
  e.g. dataset, domain, etc.
  """
  groupingEntityType: EntityType!
}

"""
The path taken when doing search across lineage
"""
enum LineageSearchPath {
  """
  Designates the tortoise lineage code path
  """
  TORTOISE
  """
  Designates the lightning lineage code path
  """
  LIGHTNING
}
