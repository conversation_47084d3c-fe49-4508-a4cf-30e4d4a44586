# Extending the GQL type system to include Long type used for dates
scalar Long

"""
Root GraphQL API Schema
"""
schema {
  query: Query
  mutation: Mutation
}

"""
Root type used for fetching DataHub Metadata
Coming soon listEntity queries for listing all entities of a given type
"""
type Query {
  """
  Fetch a Data Platform by primary key (urn)
  """
  dataPlatform(urn: String!): DataPlatform

  """
  Fetch a CorpUser, representing a DataHub platform user, by primary key (urn)
  """
  corpUser(urn: String!): CorpUser

  """
  Fetch a CorpGroup, representing a DataHub platform group by primary key (urn)
  """
  corpGroup(urn: String!): CorpGroup

  """
  Fetch a Dataset by primary key (urn)
  """
  dataset(urn: String!): Dataset

  """
  Fetch a Dataset by primary key (urn) at a point in time based on aspect versions (versionStamp)
  """
  versionedDataset(urn: String!, versionStamp: String): VersionedDataset

  """
  Fetch a Dashboard by primary key (urn)
  """
  dashboard(urn: String!): Dashboard

  """
  Fetch a Notebook by primary key (urn)
  """
  notebook(urn: String!): Notebook

  """
  Fetch a Chart by primary key (urn)
  """
  chart(urn: String!): Chart

  """
  Fetch a Data Flow (or Data Pipeline) by primary key (urn)
  """
  dataFlow(urn: String!): DataFlow

  """
  Fetch a Data Job (or Data Task) by primary key (urn)
  """
  dataJob(urn: String!): DataJob

  """
  Fetch a Tag by primary key (urn)
  """
  tag(urn: String!): Tag

  """
  Fetch a View by primary key (urn)
  """
  view(urn: String!): DataHubView

  """
  Fetch a Form by primary key (urn)
  """
  form(urn: String!): Form

  """
  Fetch a Role by primary key (urn)
  """
  role(urn: String!): Role

  """
  Fetch a Structured Property by primary key (urn)
  """
  structuredProperty(urn: String!): StructuredPropertyEntity

  """
  Fetch a ERModelRelationship by primary key (urn)
  """
  erModelRelationship(urn: String!): ERModelRelationship

  """
  Fetch a Glossary Term by primary key (urn)
  """
  glossaryTerm(urn: String!): GlossaryTerm

  """
  Fetch a Glossary Node by primary key (urn)
  """
  glossaryNode(urn: String!): GlossaryNode

  """
  Fetch an Entity Container by primary key (urn)
  """
  container(urn: String!): Container

  """
  Fetch a Domain by primary key (urn)
  """
  domain(urn: String!): Domain

  """
  Fetch an Assertion by primary key (urn)
  """
  assertion(urn: String!): Assertion

  """
  List all DataHub Access Policies
  """
  listPolicies(input: ListPoliciesInput!): ListPoliciesResult

  """
  Get all granted privileges for the given actor and resource
  """
  getGrantedPrivileges(input: GetGrantedPrivilegesInput!): Privileges

  """
  Incubating: Fetch an ML Model by primary key (urn)
  """
  mlModel(urn: String!): MLModel

  """
  Incubating: Fetch an ML Model Group by primary key (urn)
  """
  mlModelGroup(urn: String!): MLModelGroup

  """
  Incubating: Fetch a ML Feature by primary key (urn)
  """
  mlFeature(urn: String!): MLFeature

  """
  Incubating: Fetch a ML Feature Table by primary key (urn)
  """
  mlFeatureTable(urn: String!): MLFeatureTable

  """
  Incubating: Fetch a ML Primary Key by primary key (urn)
  """
  mlPrimaryKey(urn: String!): MLPrimaryKey

  """
  List all DataHub Users
  """
  listUsers(input: ListUsersInput!): ListUsersResult

  """
  List all DataHub Groups
  """
  listGroups(input: ListGroupsInput!): ListGroupsResult

  """
  Fetches the number of entities ingested by type
  """
  getEntityCounts(input: EntityCountInput): EntityCountResults

  """
  List all DataHub Domains
  """
  listDomains(input: ListDomainsInput!): ListDomainsResult

  """
  Get all GlossaryTerms without a parentNode
  """
  getRootGlossaryTerms(
    input: GetRootGlossaryEntitiesInput!
  ): GetRootGlossaryTermsResult

  """
  Get all GlossaryNodes without a parentNode
  """
  getRootGlossaryNodes(
    input: GetRootGlossaryEntitiesInput!
  ): GetRootGlossaryNodesResult

  """
  Get whether or not not an entity exists
  """
  entityExists(urn: String!): Boolean

  """
  Gets an entity based on its urn
  """
  entity(urn: String!): Entity

  """
  Gets entities based on their urns
  """
  entities(urns: [String!]!): [Entity]

  """
  List all DataHub Roles
  """
  listRoles(input: ListRolesInput!): ListRolesResult

  """
  Get invite token
  """
  getInviteToken(input: GetInviteTokenInput!): InviteToken

  """
  List all Posts
  """
  listPosts(input: ListPostsInput!): ListPostsResult

  """
  List DataHub Views owned by the current user
  """
  listMyViews(input: ListMyViewsInput!): ListViewsResult

  """
  List Global DataHub Views
  """
  listGlobalViews(input: ListGlobalViewsInput!): ListViewsResult

  """
  List Dataset Queries
  """
  listQueries(
    "Input required for listing queries"
    input: ListQueriesInput!
  ): ListQueriesResult

  """
  Get quick filters to display in auto-complete
  """
  getQuickFilters(input: GetQuickFiltersInput!): GetQuickFiltersResult

  """
  Fetch a DataProduct by primary key (urn)
  """
  dataProduct(urn: String!): DataProduct

  """
  List Custom Ownership Types
  """
  listOwnershipTypes(
    "Input required for listing custom ownership types"
    input: ListOwnershipTypesInput!
  ): ListOwnershipTypesResult!

  """
  Fetch a Data Platform Instance by primary key (urn)
  """
  dataPlatformInstance(urn: String!): DataPlatformInstance

  """
  Fetch a Business Attribute by primary key (urn)
  """
  businessAttribute(urn: String!): BusinessAttribute

  """
  Fetch all Business Attributes
  """
  listBusinessAttributes(
    input: ListBusinessAttributesInput!
  ): ListBusinessAttributesResult

  """
  Fetch a Data Process Instance by primary key (urn)
  """
  dataProcessInstance(urn: String!): DataProcessInstance

  """
  Fetch an Application by primary key (urn)
  """
  application(urn: String!): Application
}

"""
An ERModelRelationship is a high-level abstraction that dictates what datasets fields are erModelRelationshiped.
"""
type ERModelRelationship implements EntityWithRelationships & Entity {
  """
  The primary key of the role
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  Unique id for the erModelRelationship
  """
  id: String!

  """
  An additional set of read only properties
  """
  properties: ERModelRelationshipProperties

  """
  An additional set of of read write properties
  """
  editableProperties: ERModelRelationshipEditableProperties

  """
  References to internal resources related to the dataset
  """
  institutionalMemory: InstitutionalMemory

  """
  Ownership metadata of the dataset
  """
  ownership: Ownership

  """
  Status of the Dataset
  """
  status: Status

  """
  Tags used for searching dataset
  """
  tags: GlobalTags

  """
  The structured glossary terms associated with the dataset
  """
  glossaryTerms: GlossaryTerms

  """
  List of relationships between the source Entity and some destination entities with a given types
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges

  """
  No-op required for the model
  """
  lineage(input: LineageInput!): EntityLineageResult
}

"""
Additional properties about a ERModelRelationship
"""
type ERModelRelationshipEditableProperties {
  """
  Documentation of the ERModelRelationship
  """
  description: String
  """
  Display name of the ERModelRelationship
  """
  name: String
}

"""
Additional properties about a ERModelRelationship
"""
type ERModelRelationshipProperties {
  """
  The name of the ERModelRelationship used in display
  """
  name: String!
  """
  The urn of source
  """
  source: Dataset!

  """
  The urn of destination
  """
  destination: Dataset!

  """
  The relationFieldMappings
  """
  relationshipFieldMappings: [RelationshipFieldMapping!]

  """
  Created timestamp millis associated with the ERModelRelationship
  """
  createdTime: Long

  """
  Created actor urn associated with the ERModelRelationship
  """
  createdActor: Entity
}

"""
ERModelRelationship FieldMap
"""
type RelationshipFieldMapping {
  """
  left field
  """
  sourceField: String!
  """
  bfield
  """
  destinationField: String!
}

"""
Root type used for updating DataHub Metadata
Coming soon createEntity, addOwner, removeOwner mutations
"""
type Mutation {
  """
  Update the metadata about a particular Dataset
  """
  updateDataset(urn: String!, input: DatasetUpdateInput!): Dataset

  """
  Update the metadata about a batch of Datasets
  """
  updateDatasets(input: [BatchDatasetUpdateInput!]!): [Dataset]

  """
  Update the metadata about a particular Chart
  """
  updateChart(urn: String!, input: ChartUpdateInput!): Chart

  """
  Update the metadata about a particular Dashboard
  """
  updateDashboard(urn: String!, input: DashboardUpdateInput!): Dashboard

  """
  Update the metadata about a particular Notebook
  """
  updateNotebook(urn: String!, input: NotebookUpdateInput!): Notebook

  """
  Update the metadata about a particular Data Flow (Pipeline)
  """
  updateDataFlow(urn: String!, input: DataFlowUpdateInput!): DataFlow

  """
  Update the metadata about a particular Data Job (Task)
  """
  updateDataJob(urn: String!, input: DataJobUpdateInput!): DataJob

  """
  Create a new tag. Requires the 'Manage Tags' or 'Create Tags' Platform Privilege. If a Tag with the provided ID already exists,
  it will be overwritten.
  """
  createTag(
    "Inputs required to create a new Tag."
    input: CreateTagInput!
  ): String

  """
  Update the information about a particular Entity Tag
  """
  updateTag(urn: String!, input: TagUpdateInput!): Tag

  """
  Delete a Tag
  """
  deleteTag("The urn of the Tag to delete" urn: String!): Boolean

  """
  Set the hex color associated with an existing Tag
  """
  setTagColor(urn: String!, colorHex: String!): Boolean

  """
  Create a policy and returns the resulting urn
  """
  createPolicy(input: PolicyUpdateInput!): String

  """
  Update an existing policy and returns the resulting urn
  """
  updatePolicy(urn: String!, input: PolicyUpdateInput!): String

  """
  Remove an existing policy and returns the policy urn
  """
  deletePolicy(urn: String!): String

  """
  Add a tag to a particular Entity or subresource
  """
  addTag(input: TagAssociationInput!): Boolean

  """
  Add multiple tags to a particular Entity or subresource
  """
  addTags(input: AddTagsInput!): Boolean

  """
  Add tags to multiple Entities or subresources
  """
  batchAddTags(input: BatchAddTagsInput!): Boolean

  """
  Remove a tag from a particular Entity or subresource
  """
  removeTag(input: TagAssociationInput!): Boolean

  """
  Remove tags from multiple Entities or subresource
  """
  batchRemoveTags(input: BatchRemoveTagsInput!): Boolean

  """
  Add a glossary term to a particular Entity or subresource
  """
  addTerm(input: TermAssociationInput!): Boolean

  """
  Add glossary terms to multiple Entities or subresource
  """
  batchAddTerms(input: BatchAddTermsInput!): Boolean

  """
  Add multiple glossary terms to a particular Entity or subresource
  """
  addTerms(input: AddTermsInput!): Boolean

  """
  Remove a glossary term from a particular Entity or subresource
  """
  removeTerm(input: TermAssociationInput!): Boolean

  """
  Remove glossary terms from multiple Entities or subresource
  """
  batchRemoveTerms(input: BatchRemoveTermsInput!): Boolean

  """
  Add an owner to a particular Entity
  """
  addOwner(input: AddOwnerInput!): Boolean

  """
  Add owners to multiple Entities
  """
  batchAddOwners(input: BatchAddOwnersInput!): Boolean

  """
  Add multiple owners to a particular Entity
  """
  addOwners(input: AddOwnersInput!): Boolean

  """
  Remove an owner from a particular Entity
  """
  removeOwner(input: RemoveOwnerInput!): Boolean

  """
  Remove owners from multiple Entities
  """
  batchRemoveOwners(input: BatchRemoveOwnersInput!): Boolean

  """
  Add a link, or institutional memory, from a particular Entity
  """
  addLink(input: AddLinkInput!): Boolean

  """
  Remove a link, or institutional memory, from a particular Entity
  """
  removeLink(input: RemoveLinkInput!): Boolean

  """
  Incubating. Updates the description of a resource. Currently only supports Dataset Schema Fields, Containers
  """
  updateDescription(input: DescriptionUpdateInput!): Boolean

  """
  Remove a user. Requires Manage Users & Groups Platform Privilege
  """
  removeUser(urn: String!): Boolean

  """
  Change the status of a user. Requires Manage Users & Groups Platform Privilege
  """
  updateUserStatus(urn: String!, status: CorpUserStatus!): String

  """
  Remove a group. Requires Manage Users & Groups Platform Privilege
  """
  removeGroup(urn: String!): Boolean

  """
  Add members to a group
  """
  addGroupMembers(input: AddGroupMembersInput!): Boolean

  """
  Remove members from a group
  """
  removeGroupMembers(input: RemoveGroupMembersInput!): Boolean

  """
  Create a new group. Returns the urn of the newly created group. Requires the Manage Users & Groups Platform Privilege
  """
  createGroup(input: CreateGroupInput!): String

  """
  Create a new Domain. Returns the urn of the newly created Domain. Requires the 'Create Domains' or 'Manage Domains' Platform Privilege. If a Domain with the provided ID already exists,
  it will be overwritten.
  """
  createDomain(input: CreateDomainInput!): String

  """
  Moves a domain to be parented under another domain.
  """
  moveDomain(input: MoveDomainInput!): Boolean

  """
  Delete a Domain
  """
  deleteDomain("The urn of the Domain to delete" urn: String!): Boolean

  """
  Sets the Domain for a Dataset, Chart, Dashboard, Data Flow (Pipeline), or Data Job (Task). Returns true if the Domain was successfully added, or already exists. Requires the Edit Domains privilege for the Entity.
  """
  setDomain(entityUrn: String!, domainUrn: String!): Boolean

  """
  Set domain for multiple Entities
  """
  batchSetDomain(input: BatchSetDomainInput!): Boolean

  """
  Sets the Domain for a Dataset, Chart, Dashboard, Data Flow (Pipeline), or Data Job (Task). Returns true if the Domain was successfully removed, or was already removed. Requires the Edit Domains privilege for an asset.
  """
  unsetDomain(entityUrn: String!): Boolean

  """
  Create a ERModelRelationship
  """
  createERModelRelationship(
    "Input required to create a new ERModelRelationship"
    input: ERModelRelationshipUpdateInput!
  ): ERModelRelationship

  """
  Update a ERModelRelationship
  """
  updateERModelRelationship(
    "The urn of the ERModelRelationship to update"
    urn: String!
    "Input required to update an existing DataHub View"
    input: ERModelRelationshipUpdateInput!
  ): Boolean

  """
  Delete a ERModelRelationship
  """
  deleteERModelRelationship(
    "The urn of the ERModelRelationship to delete"
    urn: String!
  ): Boolean

  """
  Sets the Deprecation status for a Metadata Entity. Requires the Edit Deprecation status privilege for an entity.
  """
  updateDeprecation(
    "Input required to set deprecation for an Entity."
    input: UpdateDeprecationInput!
  ): Boolean

  """
  Updates the deprecation status for a batch of assets.
  """
  batchUpdateDeprecation(input: BatchUpdateDeprecationInput!): Boolean

  """
  Update a particular Corp User's editable properties
  """
  updateCorpUserProperties(urn: String!, input: CorpUserUpdateInput!): CorpUser

  """
  Update a particular Corp Group's editable properties
  """
  updateCorpGroupProperties(
    urn: String!
    input: CorpGroupUpdateInput!
  ): CorpGroup

  """
  Remove an assertion associated with an entity. Requires the 'Edit Assertions' privilege on the entity.
  """
  deleteAssertion(
    """
    The assertion to remove
    """
    urn: String!
  ): Boolean

  """
  Report a new operation for an asset
  """
  reportOperation(
    """
    Input required to report an operation
    """
    input: ReportOperationInput!
  ): String

  """
  Create a new GlossaryTerm. Returns the urn of the newly created GlossaryTerm. If a term with the provided ID already exists, it will be overwritten.
  """
  createGlossaryTerm(input: CreateGlossaryEntityInput!): String

  """
  Create a new GlossaryNode. Returns the urn of the newly created GlossaryNode. If a node with the provided ID already exists, it will be overwritten.
  """
  createGlossaryNode(input: CreateGlossaryEntityInput!): String

  """
  Updates the parent node of a resource. Currently only GlossaryNodes and GlossaryTerms have parentNodes.
  """
  updateParentNode(input: UpdateParentNodeInput!): Boolean

  """
  Remove a glossary entity (GlossaryTerm or GlossaryNode). Return boolean whether it was successful or not.
  """
  deleteGlossaryEntity(urn: String!): Boolean

  """
  Updates the name of the entity.
  """
  updateName(input: UpdateNameInput!): Boolean

  """
  Add multiple related Terms to a Glossary Term to establish relationships
  """
  addRelatedTerms(input: RelatedTermsInput!): Boolean

  """
  Remove multiple related Terms for a Glossary Term
  """
  removeRelatedTerms(input: RelatedTermsInput!): Boolean

  """
  Generates a token that can be shared with existing native users to reset their credentials.
  """
  createNativeUserResetToken(
    input: CreateNativeUserResetTokenInput!
  ): ResetToken

  """
  Updates the soft deleted status for a batch of assets
  """
  batchUpdateSoftDeleted(input: BatchUpdateSoftDeletedInput!): Boolean

  """
  Update the View-related settings for a user.
  """
  updateCorpUserViewsSettings(input: UpdateCorpUserViewsSettingsInput!): Boolean

  """
  Update the HomePage-related settings for a user.
  """
  updateUserHomePageSettings(input: UpdateUserHomePageSettingsInput!): Boolean

  """
  Update a user setting
  """
  updateUserSetting(input: UpdateUserSettingInput!): Boolean

  """
  Batch assign roles to users
  """
  batchAssignRole(input: BatchAssignRoleInput!): Boolean

  """
  Accept role using invite token
  """
  acceptRole(input: AcceptRoleInput!): Boolean

  """
  Create invite token
  """
  createInviteToken(input: CreateInviteTokenInput!): InviteToken

  """
  Create a post
  """
  createPost(input: CreatePostInput!): Boolean

  """
  Update or edit a post
  """
  updatePost(input: UpdatePostInput!): Boolean

  """
  Delete a post
  """
  deletePost(urn: String!): Boolean

  """
  Create a new DataHub View (Saved Filter)
  """
  createView(
    "Input required to create a new DataHub View"
    input: CreateViewInput!
  ): DataHubView

  """
  Delete a DataHub View (Saved Filter)
  """
  updateView(
    "The urn of the View to update"
    urn: String!
    "Input required to update an existing DataHub View"
    input: UpdateViewInput!
  ): DataHubView

  """
  Delete a DataHub View (Saved Filter)
  """
  deleteView("The urn of the View to delete" urn: String!): Boolean

  """
  Update lineage for an entity
  """
  updateLineage(input: UpdateLineageInput!): Boolean

  """
  Update the Embed information for a Dataset, Dashboard, or Chart.
  """
  updateEmbed(input: UpdateEmbedInput!): Boolean

  """
  Create a new Query
  """
  createQuery(
    "Inputs required to create a new Query."
    input: CreateQueryInput!
  ): QueryEntity

  """
  Update an existing Query
  """
  updateQuery(
    "The urn identifier for the query to update."
    urn: String!
    "Inputs required to update a Query."
    input: UpdateQueryInput!
  ): QueryEntity

  """
  Delete a Query by urn. This requires the 'Edit Queries' Metadata Privilege.
  """
  deleteQuery("Urn of the query to remove." urn: String!): Boolean

  """
  Create a new Data Product
  """
  createDataProduct(
    "Inputs required to create a new DataProduct."
    input: CreateDataProductInput!
  ): DataProduct

  """
  Update a Data Product
  """
  updateDataProduct(
    "The urn identifier for the Data Product to update."
    urn: String!
    "Inputs required to create a new DataProduct."
    input: UpdateDataProductInput!
  ): DataProduct

  """
  Delete a DataProduct by urn.
  """
  deleteDataProduct("Urn of the data product to remove." urn: String!): Boolean

  """
  Batch set or unset a DataProduct to a list of entities
  """
  batchSetDataProduct(
    "Input for batch setting data product"
    input: BatchSetDataProductInput!
  ): Boolean

  """
  Create a new Application
  """
  createApplication(
    "Inputs required to create a new Application."
    input: CreateApplicationInput!
  ): Application

  """
  Update a Application
  """
  updateApplication(
    "The urn identifier for the Application to update."
    urn: String!
    "Inputs required to create a new Application."
    input: UpdateApplicationInput!
  ): Application

  """
  Delete a Application by urn.
  """
  deleteApplication("Urn of the application to remove." urn: String!): Boolean

  """
  Batch set or unset a Application to a list of entities
  """
  batchSetApplication(
    "Input for batch setting application"
    input: BatchSetApplicationInput!
  ): Boolean

  """
  Create a Custom Ownership Type. This requires the 'Manage Ownership Types' Metadata Privilege.
  """
  createOwnershipType(
    "Inputs required to create a new Query."
    input: CreateOwnershipTypeInput!
  ): OwnershipTypeEntity

  """
  Update an existing Query. This requires the 'Manage Ownership Types' Metadata Privilege.
  """
  updateOwnershipType(
    "The urn identifier for the custom ownership type to update."
    urn: String!
    "Inputs required to update an existing Custom Ownership Type."
    input: UpdateOwnershipTypeInput!
  ): OwnershipTypeEntity

  """
  Delete a Custom Ownership Type by urn. This requires the 'Manage Ownership Types' Metadata Privilege.
  """
  deleteOwnershipType(
    "Urn of the Custom Ownership Type to remove."
    urn: String!
    deleteReferences: Boolean
  ): Boolean

  """
  Submit a response to a prompt from a form collecting metadata on different entities.
  Provide the urn of the entity you're submitting a form response as well as the required input.
  """
  submitFormPrompt(urn: String!, input: SubmitFormPromptInput!): Boolean

  """
  Assign a form to different entities. This will be a patch by adding this form to the list
  of forms on an entity.
  """
  batchAssignForm(input: BatchAssignFormInput!): Boolean

  """
  Creates a filter for a form to apply it to certain entities. Entities that match this filter will have
  a given form applied to them.
  This feature is ONLY supported in DataHub Cloud.
  """
  createDynamicFormAssignment(input: CreateDynamicFormAssignmentInput!): Boolean

  """
  Verifies a form on an entity when all of the required questions on the form are complete and the form
  is of type VERIFICATION.
  """
  verifyForm(input: VerifyFormInput!): Boolean

  """
  Create Business Attribute Api
  """
  createBusinessAttribute(
    "Inputs required to create a new BusinessAttribute."
    input: CreateBusinessAttributeInput!
  ): BusinessAttribute

  """
  Delete a Business Attribute by urn.
  """
  deleteBusinessAttribute(
    "Urn of the business attribute to remove."
    urn: String!
  ): Boolean

  """
  Update Business Attribute
  """
  updateBusinessAttribute(
    "The urn identifier for the Business Attribute to update."
    urn: String!
    "Inputs required to create a new Business Attribute."
    input: UpdateBusinessAttributeInput!
  ): BusinessAttribute

  """
  Add Business Attribute
  """
  addBusinessAttribute(input: AddBusinessAttributeInput!): Boolean

  """
  Remove Business Attribute
  """
  removeBusinessAttribute(input: AddBusinessAttributeInput!): Boolean

  """
  Update a particular entity's display properties
  """
  updateDisplayProperties(
    urn: String!
    input: DisplayPropertiesUpdateInput!
  ): Boolean
}

"""
A top level Metadata Entity
"""
interface Entity {
  """
  A primary key of the Metadata Entity
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  List of relationships between the source Entity and some destination entities with a given types
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult
}

"""
A Metadata Entity which is browsable, or has browse paths.
"""
interface BrowsableEntity {
  """
  The browse paths corresponding to an entity. If no Browse Paths have been generated before, this will be null.
  """
  browsePaths: [BrowsePath!]
}

"""
A top level Metadata Entity Type
"""
enum EntityType {
  """
  A Domain containing Metadata Entities
  """
  DOMAIN

  """
  The Dataset Entity
  """
  DATASET

  """
  The CorpUser Entity
  """
  CORP_USER

  """
  The CorpGroup Entity
  """
  CORP_GROUP

  """
  The DataPlatform Entity
  """
  DATA_PLATFORM

  """
  The ERModelRelationship Entity
  """
  ER_MODEL_RELATIONSHIP

  """
  The Dashboard Entity
  """
  DASHBOARD

  """
  The Notebook Entity
  """
  NOTEBOOK

  """
  The Chart Entity
  """
  CHART

  """
  The Data Flow (or Data Pipeline) Entity,
  """
  DATA_FLOW

  """
  The Data Job (or Data Task) Entity
  """
  DATA_JOB

  """
  The Tag Entity
  """
  TAG

  """
  The Glossary Term Entity
  """
  GLOSSARY_TERM

  """
  The Glossary Node Entity
  """
  GLOSSARY_NODE

  """
  A container of Metadata Entities
  """
  CONTAINER

  """
  The ML Model Entity
  """
  MLMODEL

  """
  The MLModelGroup Entity
  """
  MLMODEL_GROUP

  """
  ML Feature Table Entity
  """
  MLFEATURE_TABLE

  """
  The ML Feature Entity
  """
  MLFEATURE

  """
  The ML Primary Key Entity
  """
  MLPRIMARY_KEY

  """
  A DataHub Managed Ingestion Source
  """
  INGESTION_SOURCE

  """
  A DataHub ExecutionRequest
  """
  EXECUTION_REQUEST

  """
  A DataHub Assertion
  """
  ASSERTION

  """
  An instance of an individual run of a data job or data flow
  """
  DATA_PROCESS_INSTANCE

  """
  Data Platform Instance Entity
  """
  DATA_PLATFORM_INSTANCE

  """
  A DataHub Access Token
  """
  ACCESS_TOKEN

  """
  A DataHub Test
  """
  TEST

  """
  A DataHub Policy
  """
  DATAHUB_POLICY

  """
  A DataHub Role
  """
  DATAHUB_ROLE

  """
  A DataHub Post
  """
  POST

  """
  A Schema Field
  """
  SCHEMA_FIELD

  """
  A DataHub View
  """
  DATAHUB_VIEW

  """
  A dataset query
  """
  QUERY

  """
  A Data Product
  """
  DATA_PRODUCT

  """
  A Custom Ownership Type
  """
  CUSTOM_OWNERSHIP_TYPE

  """
  A connection to an external source.
  """
  DATAHUB_CONNECTION

  """
  A DataHub incident - SaaS only
  """
  INCIDENT

  """
  "
  A Role from an organisation
  """
  ROLE

  """
  A data contract
  """
  DATA_CONTRACT

  """
  "
  An structured property on entities
  """
  STRUCTURED_PROPERTY

  """
  "
  A form entity on entities
  """
  FORM

  """
  "
  A data type registered to DataHub
  """
  DATA_TYPE

  """
  "
  A type of entity registered to DataHub
  """
  ENTITY_TYPE

  """
  "
  A type of entity that is restricted to the user
  """
  RESTRICTED

  """
  Another entity type - refer to a provided entity type urn.
  """
  OTHER

  """
  A Business Attribute
  """
  BUSINESS_ATTRIBUTE

  """
  A set of versioned entities, representing a single source / logical entity over time
  """
  VERSION_SET

  """
  An application
  """
  APPLICATION

  """
  An DataHub Page Template
  """
  DATAHUB_PAGE_TEMPLATE

  """
  An DataHub Page Module
  """
  DATAHUB_PAGE_MODULE
}

"""
Input for the get entity counts endpoint
"""
input EntityCountInput {
  types: [EntityType!]

  """
  Optional - A View to apply when generating results
  """
  viewUrn: String
}

"""
Input for the list lineage property of an Entity
"""
input LineageInput {
  """
  The direction of the relationship, either incoming or outgoing from the source entity
  """
  direction: LineageDirection!

  """
  The starting offset of the result set
  """
  start: Int

  """
  The number of results to be returned
  """
  count: Int

  """
  Optional flag to not merge siblings in the response. They are merged by default.
  """
  separateSiblings: Boolean
  """
  An optional starting time to filter on
  """
  startTimeMillis: Long
  """
  An optional ending time to filter on
  """
  endTimeMillis: Long

  """
  If enabled, include entities that do not exist or are soft deleted.
  """
  includeGhostEntities: Boolean = false
}

"""
Input for the list relationships field of an Entity
"""
input RelationshipsInput {
  """
  The types of relationships to query, representing an OR
  """
  types: [String!]!

  """
  The direction of the relationship, either incoming or outgoing from the source entity
  """
  direction: RelationshipDirection!

  """
  The starting offset of the result set
  """
  start: Int

  """
  The number of results to be returned
  """
  count: Int

  """
  Whether to include soft-deleted, related, entities
  """
  includeSoftDelete: Boolean = true
}

"""
A list of relationship information associated with a source Entity
"""
type EntityRelationshipsResult {
  """
  Start offset of the result set
  """
  start: Int

  """
  Number of results in the returned result set
  """
  count: Int

  """
  Total number of results in the result set
  """
  total: Int

  """
  Relationships in the result set
  """
  relationships: [EntityRelationship!]!
}

"""
A relationship between two entities TODO Migrate all entity relationships to this more generic model
"""
type EntityRelationship {
  """
  The type of the relationship
  """
  type: String!

  """
  The direction of the relationship relative to the source entity
  """
  direction: RelationshipDirection!

  """
  Entity that is related via lineage
  """
  entity: Entity

  """
  An AuditStamp corresponding to the last modification of this relationship
  """
  created: AuditStamp
}

"""
A list of lineage information associated with a source Entity
"""
type EntityLineageResult {
  """
  Start offset of the result set
  """
  start: Int

  """
  Number of results in the returned result set
  """
  count: Int

  """
  Total number of results in the result set
  """
  total: Int

  """
  The number of results that were filtered out of the page (soft-deleted or non-existent)
  """
  filtered: Int

  """
  Relationships in the result set
  """
  relationships: [LineageRelationship!]!
}

"""
Metadata about a lineage relationship between two entities
"""
type LineageRelationship {
  """
  The type of the relationship
  """
  type: String!

  """
  Entity that is related via lineage
  """
  entity: Entity

  """
  Degree of relationship (number of hops to get to entity)
  """
  degree: Int!

  """
  Timestamp for when this lineage relationship was created. Could be null.
  """
  createdOn: Long

  """
  The actor who created this lineage relationship. Could be null.
  """
  createdActor: Entity

  """
  Timestamp for when this lineage relationship was last updated. Could be null.
  """
  updatedOn: Long

  """
  The actor who last updated this lineage relationship. Could be null.
  """
  updatedActor: Entity

  """
  Whether this edge is a manual edge. Could be null.
  """
  isManual: Boolean

  """
  The paths traversed for this relationship
  """
  paths: [EntityPath]
}

"""
Direction between two nodes in the lineage graph
"""
enum LineageDirection {
  """
  Upstream, or left-to-right in the lineage visualization
  """
  UPSTREAM

  """
  Downstream, or right-to-left in the lineage visualization
  """
  DOWNSTREAM
}

"""
Direction between a source and destination node
"""
enum RelationshipDirection {
  """
  A directed edge pointing at the source Entity
  """
  INCOMING

  """
  A directed edge pointing at the destination Entity
  """
  OUTGOING
}

"""
A versioned aspect, or single group of related metadata, associated with an Entity and having a unique version
"""
interface Aspect {
  """
  The version of the aspect, where zero represents the latest version
  """
  version: Long
}

"""
A time series aspect, or a group of related metadata associated with an Entity and corresponding to a particular timestamp
"""
interface TimeSeriesAspect {
  """
  The timestamp associated with the time series aspect in milliseconds
  """
  timestampMillis: Long!
}

"""
Deprecated, use relationships field instead
"""
interface EntityWithRelationships implements Entity {
  """
  A primary key associated with the Metadata Entity
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult
}

"""
A Dataset entity, which encompasses Relational Tables, Document store collections, streaming topics, and other sets of data having an independent lifecycle
"""
type Dataset implements EntityWithRelationships & Entity & BrowsableEntity {
  """
  The primary key of the Dataset
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  Standardized platform urn where the dataset is defined
  """
  platform: DataPlatform!

  """
  The parent container in which the entity resides
  """
  container: Container

  """
  Recursively get the lineage of containers for this entity
  """
  parentContainers: ParentContainersResult

  """
  Unique guid for dataset
  No longer to be used as the Dataset display name. Use properties.name instead
  """
  name: String!

  """
  An additional set of read only properties
  """
  properties: DatasetProperties

  """
  An additional set of of read write properties
  """
  editableProperties: DatasetEditableProperties

  """
  Ownership metadata of the dataset
  """
  ownership: Ownership

  """
  The deprecation status of the dataset
  """
  deprecation: Deprecation

  """
  References to internal resources related to the dataset
  """
  institutionalMemory: InstitutionalMemory

  """
  Schema metadata of the dataset, available by version number
  """
  schemaMetadata(version: Long): SchemaMetadata

  """
  Editable schema metadata of the dataset
  """
  editableSchemaMetadata: EditableSchemaMetadata

  """
  Status of the Dataset
  """
  status: Status

  """
  Embed information about the Dataset
  """
  embed: Embed

  """
  Tags used for searching dataset
  """
  tags: GlobalTags

  """
  The structured glossary terms associated with the dataset
  """
  glossaryTerms: GlossaryTerms

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  The Domain associated with the Dataset
  """
  domain: DomainAssociation

  """
  The application associated with the dataset
  """
  application: ApplicationAssociation

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  The Roles and the properties to access the dataset
  """
  access: Access

  """
  Statistics about how this Dataset is used
  The first parameter, `resource`, is deprecated and no longer needs to be provided
  """
  usageStats(resource: String, range: TimeRange): UsageQueryResult

  """
  Experimental - Summary operational & usage statistics about a Dataset
  """
  statsSummary: DatasetStatsSummary

  """
  Profile Stats resource that retrieves the events in a previous unit of time in descending order
  If no start or end time are provided, the most recent events will be returned
  """
  datasetProfiles(
    startTimeMillis: Long
    endTimeMillis: Long
    filter: FilterInput
    limit: Int
  ): [DatasetProfile!]

  """
  Operational events for an entity.
  """
  operations(
    startTimeMillis: Long
    endTimeMillis: Long
    filter: FilterInput
    limit: Int
  ): [Operation!]

  """
  Assertions associated with the Dataset
  """
  assertions(
    start: Int
    count: Int
    includeSoftDeleted: Boolean
  ): EntityAssertionsResult

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  The browse paths corresponding to the dataset. If no Browse Paths have been generated before, this will be null.
  """
  browsePaths: [BrowsePath!]

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2

  """
  Experimental! The resolved health statuses of the Dataset
  """
  health: [Health!]

  """
  Schema metadata of the dataset
  """
  schema: Schema @deprecated(reason: "Use `schemaMetadata`")

  """
  Deprecated, use properties field instead
  External URL associated with the Dataset
  """
  externalUrl: String @deprecated

  """
  Deprecated, see the properties field instead
  Environment in which the dataset belongs to or where it was generated
  Note that this field will soon be deprecated in favor of a more standardized concept of Environment
  """
  origin: FabricType! @deprecated

  """
  Deprecated, use the properties field instead
  Read only technical description for dataset
  """
  description: String @deprecated

  """
  Deprecated, do not use this field
  The logical type of the dataset ie table, stream, etc
  """
  platformNativeType: PlatformNativeType @deprecated

  """
  Deprecated, use properties instead
  Native Dataset Uri
  Uri should not include any environment specific properties
  """
  uri: String @deprecated

  """
  Deprecated, use tags field instead
  The structured tags associated with the dataset
  """
  globalTags: GlobalTags @deprecated

  """
  Sub Types that this entity implements
  """
  subTypes: SubTypes

  """
  View related properties. Only relevant if subtypes field contains view.
  """
  viewProperties: ViewProperties

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  History of datajob runs that either produced or consumed this dataset
  """
  runs(
    start: Int
    count: Int
    direction: RelationshipDirection!
  ): DataProcessInstanceResult

  """
  Metadata about the datasets siblings
  """
  siblings: SiblingProperties

  """
  Executes a search on only the siblings of an entity
  """
  siblingsSearch(input: ScrollAcrossEntitiesInput!): ScrollResults

  """
  Lineage information for the column-level. Includes a list of objects
  detailing which columns are upstream and which are downstream of each other.
  The upstream and downstream columns are from datasets.
  """
  fineGrainedLineages: [FineGrainedLineage!]

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Structured properties about this Dataset
  """
  structuredProperties: StructuredProperties
}

type RoleAssociation {
  """
  The Role entity itself
  """
  role: Role!

  """
  Reference back to the tagged urn for tracking purposes e.g. when sibling nodes are merged together
  """
  associatedUrn: String!
}

type Access {
  roles: [RoleAssociation!]
}

type Role implements Entity {
  """
  A primary key of the Metadata Entity
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  List of relationships between the source Entity and some destination entities with a given types
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Id of the Role
  """
  id: String!

  """
  Role properties to include Request Access Url
  """
  properties: RoleProperties

  """
  A standard Entity Type
  """
  actors: Actor

  isAssignedToMe: Boolean!
}

type Actor {
  """
  List of users for which the role is provisioned
  """
  users: [RoleUser!]
}

type RoleUser {
  """
  Linked corp user of a role
  """
  user: CorpUser!
}

type RoleProperties {
  """
  Name of the Role in an organisation
  """
  name: String!

  """
  Description about the role
  """
  description: String

  """
  Role type can be READ, WRITE or ADMIN
  """
  type: String

  """
  Url to request a role for a user in an organisation
  """
  requestUrl: String
}

"""
Properties related to how the entity is displayed in the Datahub UI
"""
type DisplayProperties {
  """
  Color associated with the entity in Hex. For example #FFFFFF
  """
  colorHex: String

  """
  The icon associated with the entity
  """
  icon: IconProperties
}

"""
Input for Properties describing an icon associated with an entity
"""
input IconPropertiesInput {
  """
  The source of the icon: e.g. Antd, Material, etc
  """
  iconLibrary: IconLibrary

  """
  The name of the icon
  """
  name: String

  """
  Any modifier for the icon, this will be library-specific, e.g. filled/outlined, etc
  """
  style: String
}

"""
Properties describing an icon associated with an entity
"""
type IconProperties {
  """
  The source of the icon: e.g. Antd, Material, etc
  """
  iconLibrary: IconLibrary

  """
  The name of the icon
  """
  name: String

  """
  Any modifier for the icon, this will be library-specific, e.g. filled/outlined, etc
  """
  style: String
}

enum IconLibrary {
  """
  Icons from the Material UI icon library
  """
  MATERIAL
}

type FineGrainedLineage {
  upstreams: [SchemaFieldRef!]
  downstreams: [SchemaFieldRef!]
  query: String
  transformOperation: String
}

"""
Metadata about the entity's siblings
"""
type SiblingProperties {
  """
  If this entity is the primary sibling among the sibling set
  """
  isPrimary: Boolean

  """
  The sibling entities
  """
  siblings: [Entity]
}

"""
All of the parent containers for a given entity. Returns parents with direct parent first followed by the parent's parent etc.
"""
type ParentContainersResult {
  """
  The number of containers bubbling up for this entity
  """
  count: Int!

  """
  A list of parent containers in order from direct parent, to parent's parent etc. If there are no containers, return an emty list
  """
  containers: [Container!]!
}

"""
A Dataset entity, which encompasses Relational Tables, Document store collections, streaming topics, and other sets of data having an independent lifecycle
"""
type VersionedDataset implements Entity {
  """
  The primary key of the Dataset
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  Standardized platform urn where the dataset is defined
  """
  platform: DataPlatform!

  """
  The parent container in which the entity resides
  """
  container: Container

  """
  Recursively get the lineage of containers for this entity
  """
  parentContainers: ParentContainersResult

  """
  Unique guid for dataset
  No longer to be used as the Dataset display name. Use properties.name instead
  """
  name: String!

  """
  An additional set of read only properties
  """
  properties: DatasetProperties

  """
  An additional set of of read write properties
  """
  editableProperties: DatasetEditableProperties

  """
  Ownership metadata of the dataset
  """
  ownership: Ownership

  """
  The deprecation status of the dataset
  """
  deprecation: Deprecation

  """
  References to internal resources related to the dataset
  """
  institutionalMemory: InstitutionalMemory

  """
  Editable schema metadata of the dataset
  """
  editableSchemaMetadata: EditableSchemaMetadata

  """
  Status of the Dataset
  """
  status: Status

  """
  Tags used for searching dataset
  """
  tags: GlobalTags

  """
  The structured glossary terms associated with the dataset
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the Dataset
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  Experimental! The resolved health status of the asset
  """
  health: [Health!]

  """
  Schema metadata of the dataset
  """
  schema: Schema

  """
  Sub Types that this entity implements
  """
  subTypes: SubTypes

  """
  View related properties. Only relevant if subtypes field contains view.
  """
  viewProperties: ViewProperties

  """
  Deprecated, see the properties field instead
  Environment in which the dataset belongs to or where it was generated
  Note that this field will soon be deprecated in favor of a more standardized concept of Environment
  """
  origin: FabricType! @deprecated

  """
  No-op, has to be included due to model
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult
    @deprecated
}

"""
Data Process instances that match the provided query
"""
type DataProcessInstanceResult {
  """
  The number of entities to include in result set
  """
  count: Int

  """
  The offset of the result set
  """
  start: Int

  """
  The total number of run events returned
  """
  total: Int

  """
  The data process instances that produced or consumed the entity
  """
  runs: [DataProcessInstance]
}

"""
Params to configure what list of aspects should be fetched by the aspects property
"""
input AspectParams {
  """
  Only fetch auto render aspects
  """
  autoRenderOnly: Boolean

  """
  Fetch using aspect names
  If absent, returns all aspects matching other inputs
  """
  aspectNames: [String!]
}

"""
Payload representing data about a single aspect
"""
type RawAspect {
  """
  The name of the aspect
  """
  aspectName: String!

  """
  JSON string containing the aspect's payload
  """
  payload: String

  """
  Details for the frontend on how the raw aspect should be rendered
  """
  renderSpec: AspectRenderSpec
}

"""
Details for the frontend on how the raw aspect should be rendered
"""
type AspectRenderSpec {
  """
  Format the aspect should be displayed in for the UI. Powered by the renderSpec annotation on the aspect model
  """
  displayType: String

  """
  Name to refer to the aspect type by for the UI. Powered by the renderSpec annotation on the aspect model
  """
  displayName: String

  """
  Field in the aspect payload to index into for rendering.
  """
  key: String
}

"""
Additional read only properties about a Dataset
"""
type DatasetProperties {
  """
  The name of the dataset used in display
  """
  name: String!

  """
  Fully-qualified name of the Dataset
  """
  qualifiedName: String

  """
  Environment in which the dataset belongs to or where it was generated
  Note that this field will soon be deprecated in favor of a more standardized concept of Environment
  """
  origin: FabricType!

  """
  Read only technical description for dataset
  """
  description: String

  """
  Custom properties of the Dataset
  """
  customProperties: [CustomPropertiesEntry!]

  """
  External URL associated with the Dataset
  """
  externalUrl: String

  """
  Created timestamp millis associated with the Dataset
  """
  created: Long

  """
  Actor associated with the Dataset's created timestamp
  """
  createdActor: String

  """
  Last Modified timestamp millis associated with the Dataset
  """
  lastModified: AuditStamp!

  """
  Actor associated with the Dataset's lastModified timestamp.
  Deprecated - Use lastModified.actor instead.
  """
  lastModifiedActor: String @deprecated
}

"""
Additional read only properties about a DataPlatformInstance
"""
type DataPlatformInstanceProperties {
  """
  The name of the data platform instance used in display
  """
  name: String

  """
  Read only technical description for the data platform instance
  """
  description: String

  """
  Custom properties of the data platform instance
  """
  customProperties: [CustomPropertiesEntry!]

  """
  External URL associated with the data platform instance
  """
  externalUrl: String
}

"""
A Glossary Term, or a node in a Business Glossary representing a standardized domain
data type
"""
type GlossaryTerm implements Entity {
  """
  The primary key of the glossary term
  """
  urn: String!

  """
  Ownership metadata of the glossary term
  """
  ownership: Ownership

  """
  The Domain associated with the glossary term
  """
  domain: DomainAssociation

  """
  The application associated with the glossary term
  """
  application: ApplicationAssociation

  """
  References to internal resources related to the Glossary Term
  """
  institutionalMemory: InstitutionalMemory

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  A unique identifier for the Glossary Term. Deprecated - Use properties.name field instead.
  """
  name: String! @deprecated

  """
  hierarchicalName of glossary term
  """
  hierarchicalName: String!

  """
  Additional properties associated with the Glossary Term
  """
  properties: GlossaryTermProperties

  """
  Deprecated, use properties field instead
  Details of the Glossary Term
  """
  glossaryTermInfo: GlossaryTermInfo

  """
  The deprecation status of the Glossary Term
  """
  deprecation: Deprecation

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Schema metadata of the dataset
  """
  schemaMetadata(version: Long): SchemaMetadata

  """
  Recursively get the lineage of glossary nodes for this entity
  """
  parentNodes: ParentNodesResult

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms
}

"""
Deprecated, use GlossaryTermProperties instead
Information about a glossary term
"""
type GlossaryTermInfo {
  """
  The name of the Glossary Term
  """
  name: String

  """
  Description of the glossary term
  """
  description: String

  """
  Definition of the glossary term. Deprecated - Use 'description' instead.
  """
  definition: String! @deprecated

  """
  Term Source of the glossary term
  """
  termSource: String!

  """
  Source Ref of the glossary term
  """
  sourceRef: String

  """
  Source Url of the glossary term
  """
  sourceUrl: String

  """
  Properties of the glossary term
  """
  customProperties: [CustomPropertiesEntry!]

  """
  Schema definition of glossary term
  """
  rawSchema: String
}

"""
Additional read only properties about a Glossary Term
"""
type GlossaryTermProperties {
  """
  The name of the Glossary Term
  """
  name: String!

  """
  Description of the glossary term
  """
  description: String

  """
  Definition of the glossary term. Deprecated - Use 'description' instead.
  """
  definition: String! @deprecated

  """
  Term Source of the glossary term
  """
  termSource: String!

  """
  Source Ref of the glossary term
  """
  sourceRef: String

  """
  Source Url of the glossary term
  """
  sourceUrl: String

  """
  Properties of the glossary term
  """
  customProperties: [CustomPropertiesEntry!]

  """
  Schema definition of glossary term
  """
  rawSchema: String
}

"""
A Glossary Node, or a directory in a Business Glossary represents a container of
Glossary Terms or other Glossary Nodes
"""
type GlossaryNode implements Entity {
  """
  The primary key of the glossary term
  """
  urn: String!

  """
  Ownership metadata of the glossary term
  """
  ownership: Ownership

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Additional properties associated with the Glossary Term
  """
  properties: GlossaryNodeProperties

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Recursively get the lineage of glossary nodes for this entity
  """
  parentNodes: ParentNodesResult

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Display properties for the glossary node
  """
  displayProperties: DisplayProperties

  """
  Carries information about where an entity originated from.
  """
  childrenCount: GlossaryNodeChildrenCount

  """
  Executes a search on the children of this glossary node
  """
  glossaryChildrenSearch(input: ScrollAcrossEntitiesInput!): ScrollResults
}

"""
All of the parent nodes for GlossaryTerms and GlossaryNodes
"""
type GlossaryNodeChildrenCount {
  """
  The number of child glossary terms
  """
  termsCount: Int!
  """
  The number of child glossary nodes
  """
  nodesCount: Int!
}

"""
All of the parent nodes for GlossaryTerms and GlossaryNodes
"""
type ParentNodesResult {
  """
  The number of parent nodes bubbling up for this entity
  """
  count: Int!
  """
  A list of parent nodes in order from direct parent, to parent's parent etc. If there are no nodes, return an empty list
  """
  nodes: [GlossaryNode!]!
}

"""
Additional read only properties about a Glossary Node
"""
type GlossaryNodeProperties {
  """
  The name of the Glossary Term
  """
  name: String!

  """
  Description of the glossary term
  """
  description: String

  """
  Custom properties of the Glossary Node
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
A Data Platform represents a specific third party Data System or Tool Examples include
warehouses like Snowflake, orchestrators like Airflow, and dashboarding tools like Looker
"""
type DataPlatform implements Entity {
  """
  Urn of the data platform
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  Name of the data platform
  """
  name: String!

  """
  Additional read only properties associated with a data platform
  """
  properties: DataPlatformProperties

  """
  Deprecated, use properties displayName instead
  Display name of the data platform
  """
  displayName: String @deprecated

  """
  Deprecated, use properties field instead
  Additional properties associated with a data platform
  """
  info: DataPlatformInfo @deprecated

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult
}

"""
A Data Platform instance represents an instance of a 3rd party platform like Looker, Snowflake, etc.
"""
type DataPlatformInstance implements Entity {
  """
  Urn of the data platform
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Name of the data platform
  """
  platform: DataPlatform!

  """
  The platform instance id
  """
  instanceId: String!

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Additional read only properties associated with a data platform instance
  """
  properties: DataPlatformInstanceProperties

  """
  Ownership metadata of the data platform instance
  """
  ownership: Ownership

  """
  References to internal resources related to the data platform instance
  """
  institutionalMemory: InstitutionalMemory

  """
  Tags used for searching the data platform instance
  """
  tags: GlobalTags

  """
  The deprecation status of the data platform instance
  """
  deprecation: Deprecation

  """
  Status metadata of the container
  """
  status: Status
}

"""
Deprecated, use DataPlatformProperties instead
Additional read only information about a Data Platform
"""
type DataPlatformInfo {
  """
  The platform category
  """
  type: PlatformType!

  """
  Display name associated with the platform
  """
  displayName: String

  """
  The delimiter in the dataset names on the data platform
  """
  datasetNameDelimiter: String!

  """
  A logo URL associated with the platform
  """
  logoUrl: String
}

"""
Additional read only properties about a Data Platform
"""
type DataPlatformProperties {
  """
  The platform category
  """
  type: PlatformType!

  """
  Display name associated with the platform
  """
  displayName: String

  """
  The delimiter in the dataset names on the data platform
  """
  datasetNameDelimiter: String!

  """
  A logo URL associated with the platform
  """
  logoUrl: String
}

"""
The category of a specific Data Platform
"""
enum PlatformType {
  """
  Value for a file system
  """
  FILE_SYSTEM

  """
  Value for a key value store
  """
  KEY_VALUE_STORE

  """
  Value for a message broker
  """
  MESSAGE_BROKER

  """
  Value for an object store
  """
  OBJECT_STORE

  """
  Value for an OLAP datastore
  """
  OLAP_DATASTORE

  """
  Value for a query engine
  """
  QUERY_ENGINE

  """
  Value for a relational database
  """
  RELATIONAL_DB

  """
  Value for a search engine
  """
  SEARCH_ENGINE

  """
  Value for other platforms
  """
  OTHERS
}

"""
An environment identifier for a particular Entity, ie staging or production
Note that this model will soon be deprecated in favor of a more general purpose of notion
of data environment
"""
enum FabricType {
  """
  Designates development fabrics
  """
  DEV

  """
  Designates testing fabrics
  """
  TEST

  """
  Designates quality assurance fabrics
  """
  QA

  """
  Designates user acceptance testing fabrics
  """
  UAT

  """
  Designates early integration fabrics
  """
  EI

  """
  Designates pre-production fabrics
  """
  PRE

  """
  Designates staging fabrics
  """
  STG

  """
  Designates non-production fabrics
  """
  NON_PROD

  """
  Designates production fabrics
  """
  PROD

  """
  Designates corporation fabrics
  """
  CORP

  """
  Designates review fabrics
  """
  RVW

  """
  Designates sandbox fabrics
  """
  SANDBOX
}

"""
A container of other Metadata Entities
"""
type Container implements Entity {
  """
  The primary key of the container
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  Standardized platform.
  """
  platform: DataPlatform!

  """
  Fetch an Entity Container by primary key (urn)
  """
  container: Container

  """
  Recursively get the lineage of containers for this entity
  """
  parentContainers: ParentContainersResult

  """
  Read-only properties that originate in the source data platform
  """
  properties: ContainerProperties

  """
  Read-write properties that originate in DataHub
  """
  editableProperties: ContainerEditableProperties

  """
  Ownership metadata of the dataset
  """
  ownership: Ownership

  """
  References to internal resources related to the dataset
  """
  institutionalMemory: InstitutionalMemory

  """
  Tags used for searching dataset
  """
  tags: GlobalTags

  """
  The structured glossary terms associated with the dataset
  """
  glossaryTerms: GlossaryTerms

  """
  Sub types of the container, e.g. "Database" etc
  """
  subTypes: SubTypes

  """
  The Domain associated with the Dataset
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  The deprecation status of the container
  """
  deprecation: Deprecation

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Children entities inside of the Container
  """
  entities(input: ContainerEntitiesInput): SearchResults

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Status metadata of the container
  """
  status: Status

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  The Roles and the properties to access the container
  """
  access: Access

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2
}

"""
Read-only properties that originate in the source data platform
"""
type ContainerProperties {
  """
  Display name of the Container
  """
  name: String!

  """
  System description of the Container
  """
  description: String

  """
  Custom properties of the Container
  """
  customProperties: [CustomPropertiesEntry!]

  """
  Native platform URL of the Container
  """
  externalUrl: String

  """
  Fully-qualified name of the Container
  """
  qualifiedName: String
}

"""
Read-write properties that originate in DataHub
"""
type ContainerEditableProperties {
  """
  DataHub description of the Container
  """
  description: String
}

"""
Input required to fetch the entities inside of a container.
"""
input ContainerEntitiesInput {
  """
  Optional query filter for particular entities inside the container
  """
  query: String

  """
  The offset of the result set
  """
  start: Int

  """
  The number of entities to include in result set
  """
  count: Int

  """
  Optional Facet filters to apply to the result set
  """
  filters: [FacetFilterInput!]
}

"""
The data type associated with an individual Machine Learning Feature
"""
enum MLFeatureDataType {
  USELESS
  NOMINAL
  ORDINAL
  BINARY
  COUNT
  TIME
  INTERVAL
  IMAGE
  VIDEO
  AUDIO
  TEXT
  MAP
  SEQUENCE
  SET
  CONTINUOUS
  BYTE
  UNKNOWN
}

"""
Deprecated, use Deprecation instead
Information about Dataset deprecation status
Note that this model will soon be migrated to a more general purpose Entity status
"""
type DatasetDeprecation {
  """
  Whether the dataset has been deprecated by owner
  """
  deprecated: Boolean!

  """
  The time user plan to decommission this dataset
  """
  decommissionTime: Long

  """
  Additional information about the dataset deprecation plan
  """
  note: String!

  """
  The user who will be credited for modifying this deprecation content
  """
  actor: String
}

"""
Institutional memory metadata, meaning internal links and pointers related to an Entity
"""
type InstitutionalMemory {
  """
  List of records that represent the institutional memory or internal documentation of an entity
  """
  elements: [InstitutionalMemoryMetadata!]!
}

"""
An institutional memory resource about a particular Metadata Entity
"""
type InstitutionalMemoryMetadata {
  """
  Link to a document or wiki page or another internal resource
  """
  url: String!

  """
  Label associated with the URL
  """
  label: String!

  """
  The author of this metadata
  Deprecated! Use actor instead for users or groups.
  """
  author: CorpUser! @deprecated(reason: "Use `actor`")

  """
  The author of this metadata
  """
  actor: ResolvedActor!

  """
  An AuditStamp corresponding to the creation of this resource
  """
  created: AuditStamp!

  """
  Deprecated, use label instead
  Description of the resource
  """
  description: String! @deprecated

  """
  Reference back to the owned urn for tracking purposes e.g. when sibling nodes are merged together
  """
  associatedUrn: String!
}

"""
Metadata about a Dataset schema
"""
type SchemaMetadata implements Aspect {
  """
  The logical version of the schema metadata, where zero represents the latest version
  with otherwise monotonic ordering starting at one
  """
  aspectVersion: Long

  """
  Dataset this schema metadata is associated with
  """
  datasetUrn: String

  """
  Schema name
  """
  name: String!

  """
  Platform this schema metadata is associated with
  """
  platformUrn: String!

  """
  The version of the GMS Schema metadata
  """
  version: Long!

  """
  The cluster this schema metadata is derived from
  """
  cluster: String

  """
  The SHA1 hash of the schema content
  """
  hash: String!

  """
  The native schema in the datasets platform, schemaless if it was not provided
  """
  platformSchema: PlatformSchema

  """
  Client provided a list of fields from value schema
  """
  fields: [SchemaField!]!

  """
  Client provided list of fields that define primary keys to access record
  """
  primaryKeys: [String!]

  """
  Client provided list of foreign key constraints
  """
  foreignKeys: [ForeignKeyConstraint]

  """
  The time at which the schema metadata information was created
  """
  createdAt: Long
}

"""
Metadata around a foreign key constraint between two datasets
"""
type ForeignKeyConstraint {
  """
  The human-readable name of the constraint
  """
  name: String

  """
  List of fields in the foreign dataset
  """
  foreignFields: [SchemaFieldEntity]

  """
  List of fields in this dataset
  """
  sourceFields: [SchemaFieldEntity]

  """
  The foreign dataset for easy reference
  """
  foreignDataset: Dataset
}

"""
Deprecated, use SchemaMetadata instead
Metadata about a Dataset schema
"""
type Schema {
  """
  Dataset this schema metadata is associated with
  """
  datasetUrn: String

  """
  Schema name
  """
  name: String!

  """
  Platform this schema metadata is associated with
  """
  platformUrn: String!

  """
  The version of the GMS Schema metadata
  """
  version: Long!

  """
  The cluster this schema metadata is derived from
  """
  cluster: String

  """
  The SHA1 hash of the schema content
  """
  hash: String!

  """
  The native schema in the datasets platform, schemaless if it was not provided
  """
  platformSchema: PlatformSchema

  """
  Client provided a list of fields from value schema
  """
  fields: [SchemaField!]!

  """
  Client provided list of fields that define primary keys to access record
  """
  primaryKeys: [String!]

  """
  Client provided list of foreign key constraints
  """
  foreignKeys: [ForeignKeyConstraint]

  """
  The time at which the schema metadata information was created
  """
  createdAt: Long

  """
  The time at which the schema metadata information was last ingested
  """
  lastObserved: Long
}

"""
A type of Schema, either a table schema or a key value schema
"""
union PlatformSchema = TableSchema | KeyValueSchema

"""
Information about a raw Table Schema
"""
type TableSchema {
  """
  Raw table schema
  """
  schema: String!
}

"""
Information about a raw Key Value Schema
"""
type KeyValueSchema {
  """
  Raw key schema
  """
  keySchema: String!

  """
  Raw value schema
  """
  valueSchema: String!
}

"""
Standalone schema field entity. Differs from the SchemaField struct because it is not directly nested inside a
schema field
"""
type SchemaFieldEntity implements EntityWithRelationships & Entity {
  """
  Primary key of the schema field
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Field path identifying the field in its dataset
  """
  fieldPath: String!

  """
  The field's parent.
  """
  parent: Entity!

  """
  Structured properties on this schema field
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  Business Attribute associated with the field
  """
  businessAttributes: BusinessAttributes

  """
  Documentation aspect for this schema field
  """
  documentation: Documentation

  """
  The status of the SchemaFieldEntity
  """
  status: Status

  """
  deprecation status of the schema field
  """
  deprecation: Deprecation
}

"""
Object containing structured properties for an entity
"""
type StructuredProperties {
  """
  Structured properties on this entity
  """
  properties: [StructuredPropertiesEntry!]
}

"""
Information about an individual field in a Dataset schema
"""
type SchemaField {
  """
  Flattened name of the field computed from jsonPath field
  """
  fieldPath: String!

  """
  Flattened name of a field in JSON Path notation
  """
  jsonPath: String

  """
  Human readable label for the field. Not supplied by all data sources
  """
  label: String

  """
  Indicates if this field is optional or nullable
  """
  nullable: Boolean!

  """
  Description of the field
  """
  description: String

  """
  Platform independent field type of the field
  """
  type: SchemaFieldDataType!

  """
  The native type of the field in the datasets platform as declared by platform schema
  """
  nativeDataType: String

  """
  Whether the field references its own type recursively
  """
  recursive: Boolean!

  """
  Deprecated, use tags field instead
  Tags associated with the field
  """
  globalTags: GlobalTags @deprecated

  """
  Tags associated with the field
  """
  tags: GlobalTags

  """
  Glossary terms associated with the field
  """
  glossaryTerms: GlossaryTerms

  """
  Whether the field is part of a key schema
  """
  isPartOfKey: Boolean

  """
  Whether the field is part of a partitioning key schema
  """
  isPartitioningKey: Boolean

  """
  For schema fields that have other properties that are not modeled explicitly, represented as a JSON string.
  """
  jsonProps: String

  """
  Schema field entity that exist in the database for this schema field
  """
  schemaFieldEntity: SchemaFieldEntity
}

"""
Information about schema metadata that is editable via the UI
"""
type EditableSchemaMetadata {
  """
  Editable schema field metadata
  """
  editableSchemaFieldInfo: [EditableSchemaFieldInfo!]!
}

"""
Editable schema field metadata ie descriptions, tags, etc
"""
type EditableSchemaFieldInfo {
  """
  Flattened name of a field identifying the field the editable info is applied to
  """
  fieldPath: String!

  """
  Edited description of the field
  """
  description: String

  """
  Deprecated, use tags field instead
  Tags associated with the field
  """
  globalTags: GlobalTags @deprecated

  """
  Tags associated with the field
  """
  tags: GlobalTags

  """
  Glossary terms associated with the field
  """
  glossaryTerms: GlossaryTerms
}

"""
The type associated with a single Dataset schema field
"""
enum SchemaFieldDataType {
  """
  A boolean type
  """
  BOOLEAN

  """
  A fixed bytestring type
  """
  FIXED

  """
  A string type
  """
  STRING

  """
  A string of bytes
  """
  BYTES

  """
  A number, including integers, floats, and doubles
  """
  NUMBER

  """
  A datestrings type
  """
  DATE

  """
  A timestamp type
  """
  TIME

  """
  An enum type
  """
  ENUM

  """
  A NULL type
  """
  NULL

  """
  A map collection type
  """
  MAP

  """
  An array collection type
  """
  ARRAY

  """
  An union type
  """
  UNION

  """
  An complex struct type
  """
  STRUCT
}

"""
Properties about a Dataset of type view
"""
type ViewProperties {
  """
  Whether the view is materialized or not
  """
  materialized: Boolean!

  """
  The logic associated with the view, most commonly a SQL statement
  """
  logic: String!

  """
  A formatted version of the logic associated with the view.
  For dbt, this contains the compiled SQL.
  """
  formattedLogic: String

  """
  The language in which the view logic is written, for example SQL
  """
  language: String!
}

"""
Dataset properties that are editable via the UI This represents logical metadata,
as opposed to technical metadata
"""
type DatasetEditableProperties {
  """
  Description of the Dataset
  """
  description: String

  """
  Editable name of the Dataset
  """
  name: String
}

"""
Chart properties that are editable via the UI This represents logical metadata,
as opposed to technical metadata
"""
type ChartEditableProperties {
  """
  Description of the Chart
  """
  description: String
}

"""
Dashboard properties that are editable via the UI This represents logical metadata,
as opposed to technical metadata
"""
type DashboardEditableProperties {
  """
  Description of the Dashboard
  """
  description: String
}

"""
Notebook properties that are editable via the UI This represents logical metadata,
as opposed to technical metadata
"""
type NotebookEditableProperties {
  """
  Description of the Notebook
  """
  description: String
}

"""
Data Job properties that are editable via the UI This represents logical metadata,
as opposed to technical metadata
"""
type DataJobEditableProperties {
  """
  Description of the Data Job
  """
  description: String
}

"""
Data Flow properties that are editable via the UI This represents logical metadata,
as opposed to technical metadata
"""
type DataFlowEditableProperties {
  """
  Description of the Data Flow
  """
  description: String
}

"""
Deprecated, use relationships query instead
"""
type EntityRelationshipLegacy {
  """
  Entity that is related via lineage
  """
  entity: EntityWithRelationships

  """
  An AuditStamp corresponding to the last modification of this relationship
  """
  created: AuditStamp
}

"""
Deprecated, use relationships query instead
"""
type UpstreamEntityRelationships {
  entities: [EntityRelationshipLegacy]
}

"""
Deprecated, use relationships query instead
"""
type DownstreamEntityRelationships {
  entities: [EntityRelationshipLegacy]
}

"""
Deprecated, use relationships query instead
"""
type DataFlowDataJobsRelationships {
  entities: [EntityRelationshipLegacy]
}

"""
Deprecated
The type of an edge between two Datasets
"""
enum DatasetLineageType {
  """
  Direct copy without modification
  """
  COPY

  """
  Transformed dataset
  """
  TRANSFORMED

  """
  Represents a view defined on the sources
  """
  VIEW
}

"""
The status of a particular Metadata Entity
"""
type Status {
  """
  Whether the entity is removed or not
  """
  removed: Boolean!
}

"""
Deprecated, do not use this type
The logical type associated with an individual Dataset
"""
enum PlatformNativeType {
  """
  Table
  """
  TABLE

  """
  View
  """
  VIEW

  """
  Directory in file system
  """
  DIRECTORY

  """
  Stream
  """
  STREAM

  """
  Bucket in key value store
  """
  BUCKET
}

"""
An entry in a string string map represented as a tuple
"""
type StringMapEntry {
  """
  The key of the map entry
  """
  key: String!

  """
  The value fo the map entry
  """
  value: String
}

"""
An entry in a custom properties map represented as a tuple
"""
type CustomPropertiesEntry {
  """
  The key of the map entry
  """
  key: String!

  """
  The value fo the map entry
  """
  value: String

  """
  The urn of the entity this property came from for tracking purposes e.g. when sibling nodes are merged together
  """
  associatedUrn: String!
}

"""
The origin of Ownership metadata associated with a Metadata Entity
"""
enum OwnershipSourceType {
  """
  Auditing system or audit logs
  """
  AUDIT

  """
  Database, eg GRANTS table
  """
  DATABASE

  """
  File system, eg file or directory owner
  """
  FILE_SYSTEM

  """
  Issue tracking system, eg Jira
  """
  ISSUE_TRACKING_SYSTEM

  """
  Manually provided by a user
  """
  MANUAL

  """
  Other ownership like service, eg Nuage, ACL service etc
  """
  SERVICE

  """
  SCM system, eg GIT, SVN
  """
  SOURCE_CONTROL

  """
  Other sources
  """
  OTHER
}

"""
Information about the source of Ownership metadata about a Metadata Entity
"""
type OwnershipSource {
  """
  The type of the source
  """
  type: OwnershipSourceType!

  """
  An optional reference URL for the source
  """
  url: String
}

"""
The type of the ownership relationship between a Person and a Metadata Entity
Note that this field will soon become deprecated due to low usage
"""
enum OwnershipType {
  """
  A person or group who is responsible for technical aspects of the asset.
  """
  TECHNICAL_OWNER

  """
  A person or group who is responsible for logical, or business related, aspects of the asset.
  """
  BUSINESS_OWNER

  """
  A steward, expert, or delegate responsible for the asset.
  """
  DATA_STEWARD

  """
  No specific type associated with the owner.
  """
  NONE

  """
  Associated ownership type is a custom ownership type. Please check OwnershipTypeEntity urn for custom value.
  """
  CUSTOM

  """
  A person or group that owns the data.
  Deprecated! This ownership type is no longer supported. Use TECHNICAL_OWNER instead.
  """
  DATAOWNER @deprecated

  """
  A person or group that is in charge of developing the code
  Deprecated! This ownership type is no longer supported. Use TECHNICAL_OWNER instead.
  """
  DEVELOPER @deprecated

  """
  A person or a group that overseas the operation, eg a DBA or SRE
  Deprecated! This ownership type is no longer supported. Use TECHNICAL_OWNER instead.
  """
  DELEGATE @deprecated

  """
  A person, group, or service that produces or generates the data
  Deprecated! This ownership type is no longer supported. Use TECHNICAL_OWNER instead.
  """
  PRODUCER @deprecated

  """
  A person or a group that has direct business interest
  Deprecated! Use BUSINESS_OWNER instead.
  """
  STAKEHOLDER @deprecated

  """
  A person, group, or service that consumes the data
  Deprecated! This ownership type is no longer supported.
  """
  CONSUMER @deprecated
}

"""
The state of a CorpUser
"""
enum CorpUserStatus {
  """
  A User that has been provisioned and logged in
  """
  ACTIVE

  """
  A user that has been suspended
  """
  SUSPENDED
}

union ResolvedActor = CorpUser | CorpGroup

"""
A DataHub User entity, which represents a Person on the Metadata Entity Graph
"""
type CorpUser implements Entity {
  """
  The primary key of the user
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  A username associated with the user
  This uniquely identifies the user within DataHub
  """
  username: String!

  """
  Additional read only properties about the corp user
  """
  properties: CorpUserProperties

  """
  Read write properties about the corp user
  """
  editableProperties: CorpUserEditableProperties

  """
  The status of the user
  """
  status: CorpUserStatus

  """
  The tags associated with the user
  """
  tags: GlobalTags

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Whether or not this user is a native DataHub user
  """
  isNativeUser: Boolean

  """
  Deprecated, use properties field instead
  Additional read only info about the corp user
  """
  info: CorpUserInfo @deprecated

  """
  Deprecated, use editableProperties field instead
  Read write info about the corp user
  """
  editableInfo: CorpUserEditableInfo @deprecated

  """
  Deprecated, use the tags field instead
  The structured tags associated with the user
  """
  globalTags: GlobalTags @deprecated

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Settings that a user can customize through the datahub ui
  """
  settings: CorpUserSettings

  """
  Experimental API.
  For fetching extra aspects that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges
}

"""
Settings that a user can customize through the datahub ui
"""
type CorpUserSettings {
  """
  Settings that control look and feel of the DataHub UI for the user
  """
  appearance: CorpUserAppearanceSettings

  """
  Settings related to the DataHub Views feature
  """
  views: CorpUserViewsSettings

  """
  Settings related to the home page for a user
  """
  homePage: CorpUserHomePageSettings
}

"""
Settings that control look and feel of the DataHub UI for the user
"""
type CorpUserAppearanceSettings {
  """
  Flag whether the user should see a homepage with only datasets, charts & dashboards. Intended for users
  who have less operational use cases for the datahub tool.
  """
  showSimplifiedHomepage: Boolean

  """
  Flag controlling whether the V2 UI for DataHub is shown.
  """
  showThemeV2: Boolean
}

"""
Settings related to the Views feature of DataHub.
"""
type CorpUserViewsSettings {
  """
  The default view for the User.
  """
  defaultView: DataHubView
}

"""
Settings related to the home page for a user
"""
type CorpUserHomePageSettings {
  """
  The default page template for the User.
  """
  pageTemplate: DataHubPageTemplate

  """
  List of urns of the announcements dismissed by the User.
  """
  dismissedAnnouncementUrns: [String]
}

"""
Deprecated, use CorpUserProperties instead
Additional read only info about a user
"""
type CorpUserInfo {
  """
  Whether the user is active
  """
  active: Boolean!

  """
  Display name of the user
  """
  displayName: String

  """
  Email address of the user
  """
  email: String

  """
  Title of the user
  """
  title: String

  """
  Direct manager of the user
  """
  manager: CorpUser

  """
  department id the user belong to
  """
  departmentId: Long

  """
  department name this user belong to
  """
  departmentName: String

  """
  first name of the user
  """
  firstName: String

  """
  last name of the user
  """
  lastName: String

  """
  Common name of this user, format is firstName plus lastName
  """
  fullName: String

  """
  two uppercase letters country code
  """
  countryCode: String

  """
  Custom properties of the ldap
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
Additional read only properties about a user
"""
type CorpUserProperties {
  """
  Whether the user is active
  """
  active: Boolean!

  """
  Display name of the user
  """
  displayName: String

  """
  Email address of the user
  """
  email: String

  """
  Title of the user
  """
  title: String

  """
  Direct manager of the user
  """
  manager: CorpUser

  """
  department id the user belong to
  """
  departmentId: Long

  """
  department name this user belong to
  """
  departmentName: String

  """
  first name of the user
  """
  firstName: String

  """
  last name of the user
  """
  lastName: String

  """
  Common name of this user, format is firstName plus lastName
  """
  fullName: String

  """
  two uppercase letters country code
  """
  countryCode: String

  """
  Custom properties of the ldap
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
Deprecated, use CorpUserEditableProperties instead
Additional read write info about a user
"""
type CorpUserEditableInfo {
  """
  Display name to show on DataHub
  """
  displayName: String

  """
  Title to show on DataHub
  """
  title: String

  """
  About me section of the user
  """
  aboutMe: String

  """
  Teams that the user belongs to
  """
  teams: [String!]

  """
  Skills that the user possesses
  """
  skills: [String!]

  """
  A URL which points to a picture which user wants to set as a profile photo
  """
  pictureLink: String
}

"""
Additional read write properties about a user
"""
type CorpUserEditableProperties {
  """
  Display name to show on DataHub
  """
  displayName: String

  """
  Title to show on DataHub
  """
  title: String

  """
  About me section of the user
  """
  aboutMe: String

  """
  Teams that the user belongs to
  """
  teams: [String!]

  """
  Skills that the user possesses
  """
  skills: [String!]

  """
  A URL which points to a picture which user wants to set as a profile photo
  """
  pictureLink: String

  """
  The slack handle of the user
  """
  slack: String

  """
  Phone number for the user
  """
  phone: String

  """
  Email address for the user
  """
  email: String

  """
  User persona, if present
  """
  persona: DataHubPersona

  """
  Platforms commonly used by the user, if present.
  """
  platforms: [DataPlatform!]
}

"""
Arguments provided to update a CorpUser Entity
"""
input CorpUserUpdateInput {
  """
  Display name to show on DataHub
  """
  displayName: String

  """
  Title to show on DataHub
  """
  title: String

  """
  About me section of the user
  """
  aboutMe: String

  """
  Teams that the user belongs to
  """
  teams: [String!]

  """
  Skills that the user possesses
  """
  skills: [String!]

  """
  A URL which points to a picture which user wants to set as a profile photo
  """
  pictureLink: String

  """
  The slack handle of the user
  """
  slack: String

  """
  Phone number for the user
  """
  phone: String

  """
  Email address for the user
  """
  email: String

  """
  The platforms that the user frequently works with
  """
  platformUrns: [String!]

  """
  The user's persona urn"
  """
  personaUrn: String
}

"""
A DataHub Group entity, which represents a Person on the Metadata Entity Graph
"""
type CorpGroup implements Entity {
  """
  The primary key of the group
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Group name eg wherehows dev, ask_metadata
  """
  name: String!

  """
  Ownership metadata of the Corp Group
  """
  ownership: Ownership

  """
  Additional read only properties about the group
  """
  properties: CorpGroupProperties

  """
  Additional read write properties about the group
  """
  editableProperties: CorpGroupEditableProperties

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Origin info about this group.
  """
  origin: Origin

  """
  Deprecated, use properties field instead
  Additional read only info about the group
  """
  info: CorpGroupInfo @deprecated

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges
}

"""
Deprecated, use CorpUserProperties instead
Additional read only info about a group
"""
type CorpGroupInfo {
  """
  The name to display when rendering the group
  """
  displayName: String

  """
  The description provided for the group
  """
  description: String

  """
  email of this group
  """
  email: String

  """
  Deprecated, do not use
  owners of this group
  """
  admins: [CorpUser!] @deprecated

  """
  Deprecated, use relationship IsMemberOfGroup instead
  List of ldap urn in this group
  """
  members: [CorpUser!] @deprecated

  """
  Deprecated, do not use
  List of groups urns in this group
  """
  groups: [String!] @deprecated
}

"""
Additional read only properties about a group
"""
type CorpGroupProperties {
  """
  display name of this group
  """
  displayName: String

  """
  The description provided for the group
  """
  description: String

  """
  email of this group
  """
  email: String

  """
  Slack handle for the group
  """
  slack: String
}

"""
Additional read write properties about a group
"""
type CorpGroupEditableProperties {
  """
  DataHub description of the group
  """
  description: String

  """
  Slack handle for the group
  """
  slack: String

  """
  Email address for the group
  """
  email: String

  """
  A URL which points to a picture which user wants to set as a profile photo
  """
  pictureLink: String
}

"""
Update a particular entity's display properties
"""
input DisplayPropertiesUpdateInput {
  """
  Color associated with the entity in Hex. For example #FFFFFF
  """
  colorHex: String

  """
  The icon associated with the entity
  """
  icon: IconPropertiesInput
}

"""
Arguments provided to update a CorpGroup Entity
"""
input CorpGroupUpdateInput {
  """
  DataHub description of the group
  """
  description: String

  """
  Slack handle for the group
  """
  slack: String

  """
  Email address for the group
  """
  email: String

  """
  A URL which points to a picture which user wants to set as a profile photo
  """
  pictureLink: String
}

"""
An owner of a Metadata Entity, either a user or group
"""
union OwnerType = CorpUser | CorpGroup

"""
An owner of a Metadata Entity
"""
type Owner {
  """
  Owner object
  """
  owner: OwnerType!

  """
  The type of the ownership. Deprecated - Use ownershipType field instead.
  """
  type: OwnershipType @deprecated

  """
  Ownership type information
  """
  ownershipType: OwnershipTypeEntity

  """
  Source information for the ownership
  """
  source: OwnershipSource

  """
  Reference back to the owned urn for tracking purposes e.g. when sibling nodes are merged together
  """
  associatedUrn: String!
}

"""
Ownership information about a Metadata Entity
"""
type Ownership {
  """
  List of owners of the entity
  """
  owners: [Owner!]

  """
  Audit stamp containing who last modified the record and when
  """
  lastModified: AuditStamp!
}

"""
A Tag Entity, which can be associated with other Metadata Entities and subresources
"""
type Tag implements Entity {
  """
  The primary key of the TAG
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  A unique identifier for the Tag. Deprecated - Use properties.name field instead.
  """
  name: String! @deprecated

  """
  Additional properties about the Tag
  """
  properties: TagProperties

  """
  Additional read write properties about the Tag
  Deprecated! Use 'properties' field instead.
  """
  editableProperties: EditableTagProperties @deprecated

  """
  Ownership metadata of the dataset
  """
  ownership: Ownership

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Deprecated, use properties.description field instead
  """
  description: String @deprecated

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]
}

"""
Additional read write Tag properties
Deprecated! Replaced by TagProperties.
"""
type EditableTagProperties {
  """
  A display name for the Tag
  """
  name: String

  """
  A description of the Tag
  """
  description: String
}

"""
Properties for a DataHub Tag
"""
type TagProperties {
  """
  A display name for the Tag
  """
  name: String!

  """
  A description of the Tag
  """
  description: String

  """
  An optional RGB hex code for a Tag color, e.g. #FFFFFF
  """
  colorHex: String
}

"""
An edge between a Metadata Entity and a Tag Modeled as a struct to permit
additional attributes
TODO Consider whether this query should be serviced by the relationships field
"""
type TagAssociation {
  """
  The tag itself
  """
  tag: Tag!

  """
  Reference back to the tagged urn for tracking purposes e.g. when sibling nodes are merged together
  """
  associatedUrn: String!
}

"""
Tags attached to a particular Metadata Entity
"""
type GlobalTags {
  """
  The set of tags attached to the Metadata Entity
  """
  tags: [TagAssociation!]
}

"""
The technical version associated with a given Metadata Entity
"""
type VersionTag {
  versionTag: String
}

"""
Glossary Terms attached to a particular Metadata Entity
"""
type GlossaryTerms {
  """
  The set of glossary terms attached to the Metadata Entity
  """
  terms: [GlossaryTermAssociation!]
}

"""
An edge between a Metadata Entity and a Glossary Term Modeled as a struct to permit
additional attributes
TODO Consider whether this query should be serviced by the relationships field
"""
type GlossaryTermAssociation {
  """
  The glossary term itself
  """
  term: GlossaryTerm!

  """
  The actor who is responsible for the term being added"
  """
  actor: CorpUser

  """
  Reference back to the associated urn for tracking purposes e.g. when sibling nodes are merged together
  """
  associatedUrn: String!
}

"""
Arguments provided to update a Chart Entity
"""
input ChartUpdateInput {
  """
  Update to ownership
  """
  ownership: OwnershipUpdate

  """
  Deprecated, use tags field instead
  Update to global tags
  """
  globalTags: GlobalTagsUpdate

  """
  Update to tags
  """
  tags: GlobalTagsUpdate

  """
  Update to editable properties
  """
  editableProperties: ChartEditablePropertiesUpdate
}

"""
Arguments provided to update a Dashboard Entity
"""
input DashboardUpdateInput {
  """
  Update to ownership
  """
  ownership: OwnershipUpdate

  """
  Deprecated, use tags field instead
  Update to global tags
  """
  globalTags: GlobalTagsUpdate

  """
  Update to tags
  """
  tags: GlobalTagsUpdate

  """
  Update to editable properties
  """
  editableProperties: DashboardEditablePropertiesUpdate
}

"""
Arguments provided to update a Notebook Entity
"""
input NotebookUpdateInput {
  """
  Update to ownership
  """
  ownership: OwnershipUpdate

  """
  Update to tags
  """
  tags: GlobalTagsUpdate

  """
  Update to editable properties
  """
  editableProperties: NotebookEditablePropertiesUpdate
}

"""
Arguments provided to update a Data Flow aka Pipeline Entity
"""
input DataFlowUpdateInput {
  """
  Update to ownership
  """
  ownership: OwnershipUpdate

  """
  Deprecated, use tags field instead
  Update to global tags
  """
  globalTags: GlobalTagsUpdate

  """
  Update to tags
  """
  tags: GlobalTagsUpdate

  """
  Update to editable properties
  """
  editableProperties: DataFlowEditablePropertiesUpdate
}

"""
Arguments provided to update a Data Job aka Task Entity
"""
input DataJobUpdateInput {
  """
  Update to ownership
  """
  ownership: OwnershipUpdate

  """
  Deprecated, use tags field instead
  Update to global tags
  """
  globalTags: GlobalTagsUpdate

  """
  Update to tags
  """
  tags: GlobalTagsUpdate

  """
  Update to editable properties
  """
  editableProperties: DataJobEditablePropertiesUpdate
}

"""
Arguments provided to update a Dataset Entity
"""
input DatasetUpdateInput {
  """
  Update to ownership
  """
  ownership: OwnershipUpdate

  """
  Update to deprecation status
  """
  deprecation: DatasetDeprecationUpdate

  """
  Update to institutional memory, ie documentation
  """
  institutionalMemory: InstitutionalMemoryUpdate

  """
  Deprecated, use tags field instead
  Update to global tags
  """
  globalTags: GlobalTagsUpdate

  """
  Update to tags
  """
  tags: GlobalTagsUpdate

  """
  Update to editable schema metadata of the dataset
  """
  editableSchemaMetadata: EditableSchemaMetadataUpdate

  """
  Update to editable properties
  """
  editableProperties: DatasetEditablePropertiesUpdate
}

"""
Arguments provided to batch update Dataset entities
"""
input BatchDatasetUpdateInput {
  """
  Primary key of the Dataset to which the update will be applied
  """
  urn: String!

  """
  Arguments provided to update the Dataset
  """
  update: DatasetUpdateInput!
}

"""
Update to editable schema metadata of the dataset
"""
input EditableSchemaMetadataUpdate {
  """
  Update to writable schema field metadata
  """
  editableSchemaFieldInfo: [EditableSchemaFieldInfoUpdate!]!
}

"""
Update to writable schema field metadata
"""
input EditableSchemaFieldInfoUpdate {
  """
  Flattened name of a field identifying the field the editable info is applied to
  """
  fieldPath: String!

  """
  Edited description of the field
  """
  description: String

  """
  Tags associated with the field
  """
  globalTags: GlobalTagsUpdate
}

"""
Update to writable Dataset fields
"""
input DatasetEditablePropertiesUpdate {
  """
  Writable description aka documentation for a Dataset
  """
  description: String!
  """
  Editable name of the Dataset
  """
  name: String
}

"""
Update to writable Dataset fields
"""
input ERModelRelationshipEditablePropertiesUpdate {
  """
  Display name of the ERModelRelationship
  """
  name: String

  """
  Writable description for ERModelRelationship
  """
  description: String!
}

"""
Update to writable Chart fields
"""
input ChartEditablePropertiesUpdate {
  """
  Writable description aka documentation for a Chart
  """
  description: String!
}

"""
Update to writable Notebook fields
"""
input NotebookEditablePropertiesUpdate {
  """
  Writable description aka documentation for a Notebook
  """
  description: String!
}

"""
Update to writable Dashboard fields
"""
input DashboardEditablePropertiesUpdate {
  """
  Writable description aka documentation for a Dashboard
  """
  description: String!
}

"""
Update to writable Data Job fields
"""
input DataJobEditablePropertiesUpdate {
  """
  Writable description aka documentation for a Data Job
  """
  description: String!
}

"""
Update to writable Data Flow fields
"""
input DataFlowEditablePropertiesUpdate {
  """
  Writable description aka documentation for a Data Flow
  """
  description: String!
}

"""
Deprecated, use addTag or removeTag mutation instead
Update to the Tags associated with a Metadata Entity
"""
input GlobalTagsUpdate {
  """
  The new set of tags
  """
  tags: [TagAssociationUpdate!]
}

"""
Deprecated, use addTag or removeTag mutation instead
A tag update to be applied
"""
input TagAssociationUpdate {
  """
  The tag being applied
  """
  tag: TagUpdateInput!
}

"""
Deprecated, use addTag or removeTag mutations instead
An update for a particular Tag entity
"""
input TagUpdateInput {
  """
  The primary key of the Tag
  """
  urn: String!

  """
  The display name of a Tag
  """
  name: String!

  """
  Description of the tag
  """
  description: String

  """
  Ownership metadata of the tag
  """
  ownership: OwnershipUpdate
}

"""
Input required to create a new Tag
"""
input CreateTagInput {
  """
  Optional! A custom id to use as the primary key identifier for the Tag. If not provided, a random UUID will be generated as the id.
  """
  id: String

  """
  Display name for the Tag
  """
  name: String!

  """
  Optional description for the Tag
  """
  description: String
}

"""
Input required to create/update a new ERModelRelationship
"""
input ERModelRelationshipUpdateInput {
  """
  Details about the ERModelRelationship
  """
  properties: ERModelRelationshipPropertiesInput
  """
  Update to editable properties
  """
  editableProperties: ERModelRelationshipEditablePropertiesUpdate
}

"""
Details about the ERModelRelationship
"""
input ERModelRelationshipPropertiesInput {
  """
  Details about the ERModelRelationship
  """
  name: String!
  """
  Details about the ERModelRelationship
  """
  source: String!
  """
  Details about the ERModelRelationship
  """
  destination: String!
  """
  Details about the ERModelRelationship
  """
  relationshipFieldmappings: [RelationshipFieldMappingInput!]
  """
  optional flag about the ERModelRelationship is getting create
  """
  created: Boolean
  """
  optional field to prevent created time while the ERModelRelationship is getting update
  """
  createdAt: Long
  """
  optional field to prevent create actor while the ERModelRelationship is getting update
  """
  createdBy: String
}

"""
Details about the ERModelRelationship
"""
input RelationshipFieldMappingInput {
  """
  Details about the ERModelRelationship
  """
  sourceField: String
  """
  Details about the ERModelRelationship
  """
  destinationField: String
}

"""
An update for the ownership information for a Metadata Entity
"""
input OwnershipUpdate {
  """
  The updated list of owners
  """
  owners: [OwnerUpdate!]!
}

"""
An owner to add to a Metadata Entity
TODO Add a USER or GROUP actor enum
"""
input OwnerUpdate {
  """
  The owner URN, either a corpGroup or corpuser
  """
  owner: String!

  """
  The owner type. Deprecated - Use ownershipTypeUrn field instead.
  """
  type: OwnershipType @deprecated

  """
  The urn of the ownership type entity.
  """
  ownershipTypeUrn: String
}

"""
An update for the deprecation information for a Metadata Entity
"""
input DatasetDeprecationUpdate {
  """
  Whether the dataset is deprecated
  """
  deprecated: Boolean!

  """
  The time user plan to decommission this dataset
  """
  decommissionTime: Long

  """
  Additional information about the dataset deprecation plan
  """
  note: String!
}

"""
An update for the institutional memory information for a Metadata Entity
"""
input InstitutionalMemoryUpdate {
  """
  The individual references in the institutional memory
  """
  elements: [InstitutionalMemoryMetadataUpdate!]!
}

"""
An institutional memory to add to a Metadata Entity
TODO Add a USER or GROUP actor enum
"""
input InstitutionalMemoryMetadataUpdate {
  """
  Link to a document or wiki page or another internal resource
  """
  url: String!

  """
  Description of the resource
  """
  description: String

  """
  The corp user urn of the author of the metadata
  """
  author: String!

  """
  The time at which this metadata was created
  """
  createdAt: Long
}

"""
A Notebook Metadata Entity
"""
type Notebook implements Entity & BrowsableEntity {
  """
  The primary key of the Notebook
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The Notebook tool name
  """
  tool: String!

  """
  An id unique within the Notebook tool
  """
  notebookId: String!

  """
  Additional read only information about the Notebook
  """
  info: NotebookInfo

  """
  Additional read write properties about the Notebook
  """
  editableProperties: NotebookEditableProperties

  """
  Ownership metadata of the Notebook
  """
  ownership: Ownership

  """
  Status metadata of the Notebook
  """
  status: Status

  """
  The content of this Notebook
  """
  content: NotebookContent!

  """
  The tags associated with the Notebook
  """
  tags: GlobalTags

  """
  References to internal resources related to the Notebook
  """
  institutionalMemory: InstitutionalMemory

  """
  The Domain associated with the Notebook
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Sub Types that this entity implements
  """
  subTypes: SubTypes

  """
  The structured glossary terms associated with the notebook
  """
  glossaryTerms: GlossaryTerms

  """
  Standardized platform.
  """
  platform: DataPlatform!

  """
  The browse paths corresponding to the Notebook. If no Browse Paths have been generated before, this will be null.
  """
  browsePaths: [BrowsePath!]

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]
}

"""
The actual content in a Notebook
"""
type NotebookContent {
  """
  The content of a Notebook which is composed by a list of NotebookCell
  """
  cells: [NotebookCell!]!
}

"""
The Union of every NotebookCell
"""
type NotebookCell {
  """
  The chart cell content. The will be non-null only when all other cell field is null.
  """
  chartCell: ChartCell

  """
  The text cell content. The will be non-null only when all other cell field is null.
  """
  textCell: TextCell

  """
  The query cell content. The will be non-null only when all other cell field is null.
  """
  queryChell: QueryCell

  """
  The type of this Notebook cell
  """
  type: NotebookCellType!
}

"""
The type for a NotebookCell
"""
enum NotebookCellType {
  """
  TEXT Notebook cell type. The cell context is text only.
  """
  TEXT_CELL

  """
  QUERY Notebook cell type. The cell context is query only.
  """
  QUERY_CELL

  """
  CHART Notebook cell type. The cell content is chart only.
  """
  CHART_CELL
}

"""
A Notebook cell which contains text as content
"""
type TextCell {
  """
  Title of the cell
  """
  cellTitle: String!

  """
  Unique id for the cell.
  """
  cellId: String!

  """
  Captures information about who created/last modified/deleted this TextCell and when
  """
  changeAuditStamps: ChangeAuditStamps

  """
  The actual text in a TextCell in a Notebook
  """
  text: String!
}

"""
A Notebook cell which contains Query as content
"""
type QueryCell {
  """
  Title of the cell
  """
  cellTitle: String!

  """
  Unique id for the cell.
  """
  cellId: String!

  """
  Captures information about who created/last modified/deleted this TextCell and when
  """
  changeAuditStamps: ChangeAuditStamps

  """
  Raw query to explain some specific logic in a Notebook
  """
  rawQuery: String!

  """
  Captures information about who last executed this query cell and when
  """
  lastExecuted: AuditStamp
}

"""
A Notebook cell which contains chart as content
"""
type ChartCell {
  """
  Title of the cell
  """
  cellTitle: String!

  """
  Unique id for the cell.
  """
  cellId: String!

  """
  Captures information about who created/last modified/deleted this TextCell and when
  """
  changeAuditStamps: ChangeAuditStamps
}

"""
Captures information about who created/last modified/deleted the entity and when
"""
type ChangeAuditStamps {
  """
  An AuditStamp corresponding to the creation
  """
  created: AuditStamp!

  """
  An AuditStamp corresponding to the modification
  """
  lastModified: AuditStamp!

  """
  An optional AuditStamp corresponding to the deletion
  """
  deleted: AuditStamp
}

"""
A Dashboard Metadata Entity
"""
type Dashboard implements EntityWithRelationships & Entity & BrowsableEntity {
  """
  The primary key of the Dashboard
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  The parent container in which the entity resides
  """
  container: Container

  """
  Recursively get the lineage of containers for this entity
  """
  parentContainers: ParentContainersResult

  """
  The dashboard tool name
  Note that this will soon be deprecated in favor of a standardized notion of Data Platform
  """
  tool: String!

  """
  An id unique within the dashboard tool
  """
  dashboardId: String!

  """
  Additional read only properties about the dashboard
  """
  properties: DashboardProperties

  """
  Additional read write properties about the dashboard
  """
  editableProperties: DashboardEditableProperties

  """
  Ownership metadata of the dashboard
  """
  ownership: Ownership

  """
  Status metadata of the dashboard
  """
  status: Status

  """
  Embed information about the Dashboard
  """
  embed: Embed

  """
  The deprecation status of the dashboard
  """
  deprecation: Deprecation

  """
  The tags associated with the dashboard
  """
  tags: GlobalTags

  """
  References to internal resources related to the dashboard
  """
  institutionalMemory: InstitutionalMemory

  """
  The structured glossary terms associated with the dashboard
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the Dashboard
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  The browse paths corresponding to the dashboard. If no Browse Paths have been generated before, this will be null.
  """
  browsePaths: [BrowsePath!]

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2

  """
  Experimental (Subject to breaking change) -- Statistics about how this Dashboard is used
  """
  usageStats(
    startTimeMillis: Long
    endTimeMillis: Long
    limit: Int
  ): DashboardUsageQueryResult

  """
  Experimental - Summary operational & usage statistics about a Dashboard
  """
  statsSummary: DashboardStatsSummary

  """
  Deprecated, use properties field instead
  Additional read only information about the dashboard
  """
  info: DashboardInfo @deprecated

  """
  Deprecated, use editableProperties instead
  Additional read write properties about the Dashboard
  """
  editableInfo: DashboardEditableProperties @deprecated

  """
  Deprecated, use tags field instead
  The structured tags associated with the dashboard
  """
  globalTags: GlobalTags @deprecated

  """
  Standardized platform urn where the dashboard is defined
  """
  platform: DataPlatform!

  """
  Input fields that power all the charts in the dashboard
  """
  inputFields: InputFields

  """
  Sub Types of the dashboard
  """
  subTypes: SubTypes

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  Experimental! The resolved health statuses of the asset
  """
  health: [Health!]

  """
  The forms associated with the Dataset
  """
  forms: Forms
}

"""
Deprecated, use DashboardProperties instead
Additional read only info about a Dashboard
"""
type DashboardInfo {
  """
  Display of the dashboard
  """
  name: String!

  """
  Description of the dashboard
  """
  description: String

  """
  Deprecated, use relationship Contains instead
  Charts that comprise the dashboard
  """
  charts: [Chart!]! @deprecated

  """
  Native platform URL of the dashboard
  """
  externalUrl: String

  """
  Access level for the dashboard
  Note that this will soon be deprecated for low usage
  """
  access: AccessLevel

  """
  A list of platform specific metadata tuples
  """
  customProperties: [CustomPropertiesEntry!]

  """
  The time when this dashboard last refreshed
  """
  lastRefreshed: Long

  """
  An AuditStamp corresponding to the creation of this dashboard
  """
  created: AuditStamp!

  """
  An AuditStamp corresponding to the modification of this dashboard
  """
  lastModified: AuditStamp!

  """
  An optional AuditStamp corresponding to the deletion of this dashboard
  """
  deleted: AuditStamp
}

"""
Additional read only information about a Notebook
"""
type NotebookInfo {
  """
  Display of the Notebook
  """
  title: String

  """
  Description of the Notebook
  """
  description: String

  """
  Native platform URL of the Notebook
  """
  externalUrl: String

  """
  A list of platform specific metadata tuples
  """
  customProperties: [CustomPropertiesEntry!]

  """
  Captures information about who created/last modified/deleted this Notebook and when
  """
  changeAuditStamps: ChangeAuditStamps
}

"""
Additional read only properties about a Dashboard
"""
type DashboardProperties {
  """
  Display of the dashboard
  """
  name: String!

  """
  Description of the dashboard
  """
  description: String

  """
  Native platform URL of the dashboard
  """
  externalUrl: String

  """
  Access level for the dashboard
  Note that this will soon be deprecated for low usage
  """
  access: AccessLevel

  """
  A list of platform specific metadata tuples
  """
  customProperties: [CustomPropertiesEntry!]

  """
  The time when this dashboard last refreshed
  """
  lastRefreshed: Long

  """
  An AuditStamp corresponding to the creation of this dashboard
  """
  created: AuditStamp!

  """
  An AuditStamp corresponding to the modification of this dashboard
  """
  lastModified: AuditStamp!

  """
  An optional AuditStamp corresponding to the deletion of this dashboard
  """
  deleted: AuditStamp
}

"""
A Chart Metadata Entity
"""
type Chart implements EntityWithRelationships & Entity & BrowsableEntity {
  """
  The primary key of the Chart
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  The parent container in which the entity resides
  """
  container: Container

  """
  Recursively get the lineage of containers for this entity
  """
  parentContainers: ParentContainersResult

  """
  The chart tool name
  Note that this field will soon be deprecated in favor a unified notion of Data Platform
  """
  tool: String!

  """
  An id unique within the charting tool
  """
  chartId: String!

  """
  Additional read only properties about the Chart
  """
  properties: ChartProperties

  """
  Additional read write properties about the Chart
  """
  editableProperties: ChartEditableProperties

  """
  Info about the query which is used to render the chart
  """
  query: ChartQuery

  """
  Ownership metadata of the chart
  """
  ownership: Ownership

  """
  Status metadata of the chart
  """
  status: Status

  """
  The deprecation status of the chart
  """
  deprecation: Deprecation

  """
  Embed information about the Chart
  """
  embed: Embed

  """
  The tags associated with the chart
  """
  tags: GlobalTags

  """
  References to internal resources related to the dashboard
  """
  institutionalMemory: InstitutionalMemory

  """
  The structured glossary terms associated with the dashboard
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the Chart
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Not yet implemented.

  Experimental - Summary operational & usage statistics about a Chart
  """
  statsSummary: ChartStatsSummary

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  The browse paths corresponding to the chart. If no Browse Paths have been generated before, this will be null.
  """
  browsePaths: [BrowsePath!]

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2

  """
  Deprecated, use properties field instead
  Additional read only information about the chart
  """
  info: ChartInfo @deprecated

  """
  Deprecated, use editableProperties field instead
  Additional read write information about the Chart
  """
  editableInfo: ChartEditableProperties @deprecated

  """
  Deprecated, use tags instead
  The structured tags associated with the chart
  """
  globalTags: GlobalTags @deprecated

  """
  Standardized platform urn where the chart is defined
  """
  platform: DataPlatform!

  """
  Input fields to power the chart
  """
  inputFields: InputFields

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Sub Types that this entity implements
  """
  subTypes: SubTypes

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  Experimental! The resolved health statuses of the asset
  """
  health: [Health!]

  """
  The forms associated with the Dataset
  """
  forms: Forms
}

"""
Deprecated, use ChartProperties instead
Additional read only information about the chart
"""
type ChartInfo {
  """
  Display name of the chart
  """
  name: String!

  """
  Description of the chart
  """
  description: String

  """
  Deprecated, use relationship Consumes instead
  Data sources for the chart
  """
  inputs: [Dataset!] @deprecated

  """
  Native platform URL of the chart
  """
  externalUrl: String

  """
  Access level for the chart
  """
  type: ChartType

  """
  Access level for the chart
  """
  access: AccessLevel

  """
  A list of platform specific metadata tuples
  """
  customProperties: [CustomPropertiesEntry!]

  """
  The time when this chart last refreshed
  """
  lastRefreshed: Long

  """
  An AuditStamp corresponding to the creation of this chart
  """
  created: AuditStamp!

  """
  An AuditStamp corresponding to the modification of this chart
  """
  lastModified: AuditStamp!

  """
  An optional AuditStamp corresponding to the deletion of this chart
  """
  deleted: AuditStamp
}

"""
Additional read only properties about the chart
"""
type ChartProperties {
  """
  Display name of the chart
  """
  name: String!

  """
  Description of the chart
  """
  description: String

  """
  Native platform URL of the chart
  """
  externalUrl: String

  """
  Access level for the chart
  """
  type: ChartType

  """
  Access level for the chart
  """
  access: AccessLevel

  """
  A list of platform specific metadata tuples
  """
  customProperties: [CustomPropertiesEntry!]

  """
  The time when this chart last refreshed
  """
  lastRefreshed: Long

  """
  An AuditStamp corresponding to the creation of this chart
  """
  created: AuditStamp!

  """
  An AuditStamp corresponding to the modification of this chart
  """
  lastModified: AuditStamp!

  """
  An optional AuditStamp corresponding to the deletion of this chart
  """
  deleted: AuditStamp
}

"""
The access level for a Metadata Entity, either public or private
"""
enum AccessLevel {
  """
  Publicly available
  """
  PUBLIC

  """
  Restricted to a subset of viewers
  """
  PRIVATE
}

"""
The type of a Chart Entity
"""
enum ChartType {
  """
  Bar graph
  """
  BAR

  """
  Pie chart
  """
  PIE

  """
  Scatter plot
  """
  SCATTER

  """
  Table
  """
  TABLE

  """
  Markdown formatted text
  """
  TEXT

  """
  A line chart
  """
  LINE

  """
  An area chart
  """
  AREA

  """
  A histogram chart
  """
  HISTOGRAM

  """
  A box plot chart
  """
  BOX_PLOT

  """
  A word cloud chart
  """
  WORD_CLOUD

  """
  A Cohort Analysis chart
  """
  COHORT
}

"""
The query that was used to populate a Chart
"""
type ChartQuery {
  """
  Raw query to build a chart from input datasets
  """
  rawQuery: String!

  """
  The type of the chart query
  """
  type: ChartQueryType!
}

"""
The type of the Chart Query
"""
enum ChartQueryType {
  """
  Standard ANSI SQL
  """
  SQL

  """
  LookML
  """
  LOOKML
}

"""
A Data Flow Metadata Entity, representing an set of pipelined Data Job or Tasks required
to produce an output Dataset Also known as a Data Pipeline
"""
type DataFlow implements EntityWithRelationships & Entity & BrowsableEntity {
  """
  The primary key of a Data Flow
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  Workflow orchestrator ei Azkaban, Airflow
  """
  orchestrator: String!

  """
  Id of the flow
  """
  flowId: String!

  """
  Cluster of the flow
  """
  cluster: String!

  """
  Additional read only properties about a Data flow
  """
  properties: DataFlowProperties

  """
  Additional read write properties about a Data Flow
  """
  editableProperties: DataFlowEditableProperties

  """
  Ownership metadata of the flow
  """
  ownership: Ownership

  """
  The tags associated with the dataflow
  """
  tags: GlobalTags

  """
  Status metadata of the dataflow
  """
  status: Status

  """
  The deprecation status of the Data Flow
  """
  deprecation: Deprecation

  """
  References to internal resources related to the dashboard
  """
  institutionalMemory: InstitutionalMemory

  """
  The structured glossary terms associated with the dashboard
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the DataFlow
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  The parent container in which the entity resides
  """
  container: Container

  """
  Recursively get the lineage of containers for this entity
  """
  parentContainers: ParentContainersResult

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  The browse paths corresponding to the data flow. If no Browse Paths have been generated before, this will be null.
  """
  browsePaths: [BrowsePath!]

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2

  """
  Deprecated, use properties field instead
  Additional read only information about a Data flow
  """
  info: DataFlowInfo @deprecated

  """
  Deprecated, use tags field instead
  The structured tags associated with the dataflow
  """
  globalTags: GlobalTags @deprecated

  """
  Deprecated, use relationship IsPartOf instead
  Data Jobs
  """
  dataJobs: DataFlowDataJobsRelationships @deprecated

  """
  Standardized platform urn where the datflow is defined
  """
  platform: DataPlatform!

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  Experimental! The resolved health statuses of the asset
  """
  health: [Health!]

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges

  """
  Sub Types that this entity implements
  """
  subTypes: SubTypes
}

"""
Deprecated, use DataFlowProperties instead
Additional read only properties about a Data Flow aka Pipeline
"""
type DataFlowInfo {
  """
  Display name of the flow
  """
  name: String!

  """
  Description of the flow
  """
  description: String

  """
  Optional project or namespace associated with the flow
  """
  project: String

  """
  External URL associated with the DataFlow
  """
  externalUrl: String

  """
  A list of platform specific metadata tuples
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
Additional read only properties about a Data Flow aka Pipeline
"""
type DataFlowProperties {
  """
  Display name of the flow
  """
  name: String!

  """
  Description of the flow
  """
  description: String

  """
  Optional project or namespace associated with the flow
  """
  project: String

  """
  External URL associated with the DataFlow
  """
  externalUrl: String

  """
  A list of platform specific metadata tuples
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
A Data Job Metadata Entity, representing an individual unit of computation or Task
to produce an output Dataset Always part of a parent Data Flow aka Pipeline
"""
type DataJob implements EntityWithRelationships & Entity & BrowsableEntity {
  """
  The primary key of the Data Job
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Sub Types that this entity implements
  """
  subTypes: SubTypes

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  Deprecated, use relationship IsPartOf instead
  The associated data flow
  """
  dataFlow: DataFlow

  """
  Id of the job
  """
  jobId: String!

  """
  Additional read only properties associated with the Data Job
  """
  properties: DataJobProperties

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  The parent container in which the entity resides
  """
  container: Container

  """
  Recursively get the lineage of containers for this entity
  """
  parentContainers: ParentContainersResult

  """
  Additional read write properties associated with the Data Job
  """
  editableProperties: DataJobEditableProperties

  """
  The tags associated with the DataJob
  """
  tags: GlobalTags

  """
  Ownership metadata of the job
  """
  ownership: Ownership

  """
  Status metadata of the DataJob
  """
  status: Status

  """
  The deprecation status of the Data Flow
  """
  deprecation: Deprecation

  """
  References to internal resources related to the dashboard
  """
  institutionalMemory: InstitutionalMemory

  """
  The structured glossary terms associated with the dashboard
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the Data Job
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  The browse paths corresponding to the data job. If no Browse Paths have been generated before, this will be null.
  """
  browsePaths: [BrowsePath!]

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2

  """
  Deprecated, use properties field instead
  Additional read only information about a Data processing job
  """
  info: DataJobInfo @deprecated

  """
  Information about the inputs and outputs of a Data processing job including column-level lineage.
  """
  inputOutput: DataJobInputOutput

  """
  Deprecated, use the tags field instead
  The structured tags associated with the DataJob
  """
  globalTags: GlobalTags @deprecated

  """
  History of runs of this task
  """
  runs(start: Int, count: Int): DataProcessInstanceResult

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  Experimental! The resolved health statuses of the asset
  """
  health: [Health!]

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Data Transform Logic associated with the Data Job
  """
  dataTransformLogic: DataTransformLogic
}

"""
A DataProcessInstance Metadata Entity, representing an individual run of
a task or datajob.
"""
type DataProcessInstance implements EntityWithRelationships & Entity {
  """
  The primary key of the DataProcessInstance
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Status metadata of the data process instance
  """
  status: Status

  """
  The history of state changes for the run
  """
  state(
    startTimeMillis: Long
    endTimeMillis: Long
    limit: Int
  ): [DataProcessRunEvent]

  """
  When the run was kicked off
  """
  created: AuditStamp @deprecated(reason: "Use `properties.created`")

  """
  The name of the data process
  """
  name: String @deprecated(reason: "Use `properties.name`")

  """
  Edges extending from this entity.
  In the UI, used for inputs, outputs and parentTemplate
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  The link to view the task run in the source system
  """
  externalUrl: String
}

"""
A state change event in the data process instance lifecycle
"""
type DataProcessRunEvent implements TimeSeriesAspect {
  """
  The status of the data process instance
  """
  status: DataProcessRunStatus

  """
  The try number that this instance run is in
  """
  attempt: Int

  """
  The result of a run
  """
  result: DataProcessInstanceRunResult

  """
  The timestamp associated with the run event in milliseconds
  """
  timestampMillis: Long!

  """
  The duration of the run in milliseconds
  """
  durationMillis: Long
}

"""
The status of the data process instance
"""
enum DataProcessRunStatus {
  """
  The data process instance has started but not completed
  """
  STARTED

  """
  The data process instance has completed
  """
  COMPLETE
}

"""
the result of a run, part of the run state
"""
type DataProcessInstanceRunResult {
  """
  The outcome of the run
  """
  resultType: DataProcessInstanceRunResultType

  """
  The outcome of the run in the data platforms native language
  """
  nativeResultType: String
}

"""
The result of the data process run
"""
enum DataProcessInstanceRunResultType {
  """
  The run finished successfully
  """
  SUCCESS

  """
  The run finished in failure
  """
  FAILURE

  """
  The run was skipped
  """
  SKIPPED

  """
  The run failed and is up for retry
  """
  UP_FOR_RETRY
}

"""
Deprecated, use DataJobProperties instead
Additional read only information about a Data Job aka Task
"""
type DataJobInfo {
  """
  Job display name
  """
  name: String!

  """
  Job description
  """
  description: String

  """
  External URL associated with the DataJob
  """
  externalUrl: String

  """
  A list of platform specific metadata tuples
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
Additional read only properties about a Data Job aka Task
"""
type DataJobProperties {
  """
  Job display name
  """
  name: String!

  """
  Job description
  """
  description: String

  """
  External URL associated with the DataJob
  """
  externalUrl: String

  """
  A list of platform specific metadata tuples
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
The lineage information for a DataJob
TODO Rename this to align with other Lineage models
"""
type DataJobInputOutput {
  """
  Deprecated, use relationship Consumes instead
  Input datasets produced by the data job during processing
  """
  inputDatasets: [Dataset!] @deprecated

  """
  Deprecated, use relationship Produces instead
  Output datasets produced by the data job during processing
  """
  outputDatasets: [Dataset!] @deprecated

  """
  Deprecated, use relationship DownstreamOf instead
  Input datajobs that this data job depends on
  """
  inputDatajobs: [DataJob!] @deprecated

  """
  Lineage information for the column-level. Includes a list of objects
  detailing which columns are upstream and which are downstream of each other.
  The upstream and downstream columns are from datasets.
  """
  fineGrainedLineages: [FineGrainedLineage!]
}

"""
Information about a transformation applied to data assets
"""
type DataTransform {
  """
  The transformation may be defined by a query statement
  """
  queryStatement: QueryStatement
}

"""
Information about transformations applied to data assets
"""
type DataTransformLogic {
  """
  List of transformations applied
  """
  transforms: [DataTransform!]!
}

"""
Information about individual user usage of a Dataset
"""
type UserUsageCounts {
  """
  The user of the Dataset
  """
  user: CorpUser

  """
  The number of queries issued by the user
  """
  count: Int

  """
  The extracted user email
  Note that this field will soon be deprecated and merged with user
  """
  userEmail: String
}

"""
The result of a Dataset usage query
"""
type UsageQueryResult {
  """
  A set of relevant time windows for use in displaying usage statistics
  """
  buckets: [UsageAggregation]

  """
  A set of rolled up aggregations about the Dataset usage
  """
  aggregations: UsageQueryResultAggregations
}

"""
A set of rolled up aggregations about the Dataset usage
"""
type UsageQueryResultAggregations {
  """
  The count of unique Dataset users within the queried time range
  """
  uniqueUserCount: Int

  """
  The specific per user usage counts within the queried time range
  """
  users: [UserUsageCounts]

  """
  The specific per field usage counts within the queried time range
  """
  fields: [FieldUsageCounts]

  """
  The total number of queries executed within the queried time range
  Note that this field will likely be deprecated in favor of a totalQueries field
  """
  totalSqlQueries: Int
}

"""
An aggregation of Dataset usage statistics
"""
type UsageAggregation {
  """
  The time window start time
  """
  bucket: Long

  """
  The time window span
  """
  duration: WindowDuration

  """
  The resource urn associated with the usage information, eg a Dataset urn
  """
  resource: String

  """
  The rolled up usage metrics
  """
  metrics: UsageAggregationMetrics
}

"""
Rolled up metrics about Dataset usage over time
"""
type UsageAggregationMetrics {
  """
  The unique number of users who have queried the dataset within the time range
  """
  uniqueUserCount: Int

  """
  Usage statistics within the time range by user
  """
  users: [UserUsageCounts]

  """
  The total number of queries issued against the dataset within the time range
  """
  totalSqlQueries: Int

  """
  A set of common queries issued against the dataset within the time range
  """
  topSqlQueries: [String]

  """
  Per field usage statistics within the time range
  """
  fields: [FieldUsageCounts]
}

"""
The usage for a particular Dataset field
"""
type FieldUsageCounts {
  """
  The path of the field
  """
  fieldName: String

  """
  The count of usages
  """
  count: Int
}

"""
Experimental - subject to change. A summary of usage metrics about a Dataset.
"""
type DatasetStatsSummary {
  """
  The query count in the past 30 days
  """
  queryCountLast30Days: Int

  """
  The unique user count in the past 30 days
  """
  uniqueUserCountLast30Days: Int

  """
  The top users in the past 30 days
  """
  topUsersLast30Days: [CorpUser!]
}

"""
Information about individual user usage of a Dashboard
"""
type DashboardUserUsageCounts {
  """
  The user of the Dashboard
  """
  user: CorpUser

  """
  number of times dashboard has been viewed by the user
  """
  viewsCount: Int

  """
  number of dashboard executions by the user
  """
  executionsCount: Int

  """
  Normalized numeric metric representing user's dashboard usage
  Higher value represents more usage
  """
  usageCount: Int
}

"""
The result of a dashboard usage query
"""
type DashboardUsageQueryResult {
  """
  A set of relevant time windows for use in displaying usage statistics
  """
  buckets: [DashboardUsageAggregation]

  """
  A set of rolled up aggregations about the dashboard usage
  """
  aggregations: DashboardUsageQueryResultAggregations

  """
  A set of absolute dashboard usage metrics
  """
  metrics: [DashboardUsageMetrics!]
}

"""
A set of rolled up aggregations about the Dashboard usage
"""
type DashboardUsageQueryResultAggregations {
  """
  The count of unique Dashboard users within the queried time range
  """
  uniqueUserCount: Int

  """
  The specific per user usage counts within the queried time range
  """
  users: [DashboardUserUsageCounts]

  """
  The total number of dashboard views within the queried time range
  """
  viewsCount: Int

  """
  The total number of dashboard executions within the queried time range
  """
  executionsCount: Int
}

"""
A set of absolute dashboard usage metrics
"""
type DashboardUsageMetrics implements TimeSeriesAspect {
  """
  The time at which the metrics were reported
  """
  timestampMillis: Long!

  """
  The total number of times dashboard has been favorited
  FIXME: Qualifies as Popularity Metric rather than Usage Metric?
  """
  favoritesCount: Int

  """
  The total number of dashboard views
  """
  viewsCount: Int

  """
  The total number of dashboard execution
  """
  executionsCount: Int

  """
  The time when this dashboard was last viewed
  """
  lastViewed: Long
}

"""
An aggregation of Dashboard usage statistics
"""
type DashboardUsageAggregation {
  """
  The time window start time
  """
  bucket: Long

  """
  The time window span
  """
  duration: WindowDuration

  """
  The resource urn associated with the usage information, eg a Dashboard urn
  """
  resource: String

  """
  The rolled up usage metrics
  """
  metrics: DashboardUsageAggregationMetrics
}

"""
Rolled up metrics about Dashboard usage over time
"""
type DashboardUsageAggregationMetrics {
  """
  The unique number of dashboard users within the time range
  """
  uniqueUserCount: Int

  """
  The total number of dashboard views within the time range
  """
  viewsCount: Int

  """
  The total number of dashboard executions within the time range
  """
  executionsCount: Int
}

"""
Experimental - subject to change. A summary of usage metrics about a Dashboard.
"""
type DashboardStatsSummary {
  """
  The total view count for the dashboard
  """
  viewCount: Int

  """
  The view count in the last 30 days
  """
  viewCountLast30Days: Int
  """
  The unique user count in the past 30 days
  """
  uniqueUserCountLast30Days: Int
  """
  The top users in the past 30 days
  """
  topUsersLast30Days: [CorpUser!]
}

"""
Experimental - subject to change. A summary of usage metrics about a Chart.
"""
type ChartStatsSummary {
  """
  The total view count for the chart
  """
  viewCount: Int

  """
  The view count in the last 30 days
  """
  viewCountLast30Days: Int
  """
  The unique user count in the past 30 days
  """
  uniqueUserCountLast30Days: Int

  """
  The top users in the past 30 days
  """
  topUsersLast30Days: [CorpUser!]
}

"""
The duration of a fixed window of time
"""
enum WindowDuration {
  """
  A one day window
  """
  DAY

  """
  A one week window
  """
  WEEK

  """
  A one month window
  """
  MONTH

  """
  A one year window
  """
  YEAR
}

"""
A time range used in fetching Usage statistics
"""
enum TimeRange {
  """
  Last day
  """
  DAY

  """
  Last week
  """
  WEEK

  """
  Last month
  """
  MONTH

  """
  Last quarter
  """
  QUARTER

  """
  Last year
  """
  YEAR

  """
  All time
  """
  ALL
}

"""
A Dataset Profile associated with a Dataset, containing profiling statistics about the Dataset
"""
type DatasetProfile implements TimeSeriesAspect {
  """
  The time at which the profile was reported
  """
  timestampMillis: Long!

  """
  An optional row count of the Dataset
  """
  rowCount: Long

  """
  An optional column count of the Dataset
  """
  columnCount: Long

  """
  The storage size in bytes
  """
  sizeInBytes: Long

  """
  An optional set of per field statistics obtained in the profile
  """
  fieldProfiles: [DatasetFieldProfile!]

  """
  Information about the partition that was profiled
  """
  partitionSpec: PartitionSpec
}

"""
A quantile along with its corresponding value
"""
type Quantile {
  """
  Quantile. E.g. "0.25" for the 25th percentile
  """
  quantile: String!
  """
  The value of the quantile
  """
  value: String!
}

"""
A frequency distribution of a specific value within a dataset
"""
type ValueFrequency {
  """
  Specific value. For numeric colums, the value will contain a strigified value
  """
  value: String!
  """
  Volume of the value
  """
  frequency: Long!
}

"""
An individual Dataset Field Profile
"""
type DatasetFieldProfile {
  """
  The standardized path of the field
  """
  fieldPath: String!

  """
  The unique value count for the field across the Dataset
  """
  uniqueCount: Long

  """
  The proportion of rows with unique values across the Dataset
  """
  uniqueProportion: Float

  """
  The number of NULL row values across the Dataset
  """
  nullCount: Long

  """
  The proportion of rows with NULL values across the Dataset
  """
  nullProportion: Float

  """
  The min value for the field
  """
  min: String

  """
  The max value for the field
  """
  max: String

  """
  The mean value for the field
  """
  mean: String

  """
  The median value for the field
  """
  median: String

  """
  The standard deviation for the field
  """
  stdev: String

  """
  A set of sample values for the field
  """
  sampleValues: [String!]

  """
  Sorted list of quantile cutoffs for the field, in ascending order
  Only for numerical columns
  """
  quantiles: [Quantile!]

  """
  Volume of each column value for a low-cardinality / categorical field
  """
  distinctValueFrequencies: [ValueFrequency!]
}

"""
Information about the partition being profiled
"""
type PartitionSpec {
  """
  The partition type
  """
  type: PartitionType!

  """
  The partition identifier
  """
  partition: String

  """
  The optional time window partition information - required if type is TIMESTAMP_FIELD.
  """
  timePartition: TimeWindow
}

enum PartitionType {
  FULL_TABLE
  QUERY
  PARTITION
}

"""
A time window with a finite start and end time
"""
type TimeWindow {
  """
  The start time of the time window
  """
  startTimeMillis: Long!

  """
  The end time of the time window
  """
  durationMillis: Long!
}

"""
An assertion represents a programmatic validation, check, or test performed periodically against another Entity.
"""
type Assertion implements EntityWithRelationships & Entity {
  """
  The primary key of the Assertion
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  Standardized platform urn where the assertion is evaluated
  """
  platform: DataPlatform!

  """
  Details about assertion
  """
  info: AssertionInfo

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Lifecycle events detailing individual runs of this assertion. If startTimeMillis & endTimeMillis are not provided, the most
  recent events will be returned.
  """
  runEvents(
    status: AssertionRunStatus
    startTimeMillis: Long
    endTimeMillis: Long
    filter: FilterInput
    limit: Int
  ): AssertionRunEventsResult

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  Status metadata of the assertion
  """
  status: Status

  """
  The standard tags for the Assertion
  """
  tags: GlobalTags

  """
  Experimental API.
  For fetching extra aspects that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]
}

"""
Type of assertion. Assertion types can evolve to span Datasets, Flows (Pipelines), Models, Features etc.
"""
type AssertionInfo {
  """
  Top-level type of the assertion.
  """
  type: AssertionType!

  """
  Dataset-specific assertion information
  """
  datasetAssertion: DatasetAssertionInfo

  """
  An optional human-readable description of the assertion
  """
  description: String

  """
  URL where assertion details are available
  """
  externalUrl: String
}

"""
Detailed information about a Dataset Assertion
"""
type DatasetAssertionInfo {
  """
  The urn of the dataset that the assertion is related to
  """
  datasetUrn: String!

  """
  The scope of the Dataset assertion.
  """
  scope: DatasetAssertionScope!

  """
  The fields serving as input to the assertion. Empty if there are none.
  """
  fields: [SchemaFieldRef!]

  """
  Standardized assertion operator
  """
  aggregation: AssertionStdAggregation

  """
  Standardized assertion operator
  """
  operator: AssertionStdOperator!

  """
  Standard parameters required for the assertion. e.g. min_value, max_value, value, columns
  """
  parameters: AssertionStdParameters

  """
  The native operator for the assertion. For Great Expectations, this will contain the original expectation name.
  """
  nativeType: String

  """
  Native parameters required for the assertion.
  """
  nativeParameters: [StringMapEntry!]

  """
  Logic comprising a raw, unstructured assertion.
  """
  logic: String
}

"""
A Dataset schema field (i.e. column)
"""
type SchemaFieldRef {
  """
  A schema field urn
  """
  urn: String!

  """
  A schema field path
  """
  path: String!
}

"""
An event representing an event in the assertion evaluation lifecycle.
"""
type AssertionRunEvent implements TimeSeriesAspect {
  """
  The time at which the assertion was evaluated
  """
  timestampMillis: Long!

  """
  The time at which the run event was last observed by the DataHub system - ie, when it was reported by external systems
  """
  lastObservedMillis: Long

  """
  Urn of assertion which is evaluated
  """
  assertionUrn: String!

  """
  Urn of entity on which the assertion is applicable
  """
  asserteeUrn: String!

  """
  Native (platform-specific) identifier for this run
  """
  runId: String!

  """
  The status of the assertion run as per this timeseries event
  """
  status: AssertionRunStatus!

  """
  Specification of the batch which this run is evaluating
  """
  batchSpec: BatchSpec

  """
  Information about the partition that was evaluated
  """
  partitionSpec: PartitionSpec

  """
  Runtime parameters of evaluation
  """
  runtimeContext: [StringMapEntry!]

  """
  Results of assertion, present if the status is COMPLETE
  """
  result: AssertionResult
}

"""
The result of evaluating an assertion.
"""
type AssertionResult {
  """
  The final result, e.g. either SUCCESS or FAILURE.
  """
  type: AssertionResultType!

  """
  Number of rows for evaluated batch
  """
  rowCount: Long

  """
  Number of rows with missing value for evaluated batch
  """
  missingCount: Long

  """
  Number of rows with unexpected value for evaluated batch
  """
  unexpectedCount: Long

  """
  Observed aggregate value for evaluated batch
  """
  actualAggValue: Float

  """
  URL where full results are available
  """
  externalUrl: String

  """
  Native results / properties of evaluation
  """
  nativeResults: [StringMapEntry!]

  """
  Error details, if type is ERROR
  """
  error: AssertionResultError
}

"""
An error encountered when evaluating an AssertionResult
"""
type AssertionResultError {
  """
  The type of error encountered
  """
  type: AssertionResultErrorType!

  """
  Additional metadata depending on the type of error
  """
  properties: [StringMapEntry!]
}

"""
The type of error encountered when evaluating an AssertionResult
"""
enum AssertionResultErrorType {
  """
  Source is unreachable
  """
  SOURCE_CONNECTION_ERROR

  """
  Source query failed to execute
  """
  SOURCE_QUERY_FAILED

  """
  Invalid parameters were detected
  """
  INVALID_PARAMETERS

  """
  Insufficient data to evaluate assertion
  """
  INSUFFICIENT_DATA

  """
  Event type not supported by the specified source
  """
  INVALID_SOURCE_TYPE

  """
  Platform not supported
  """
  UNSUPPORTED_PLATFORM

  """
  Error while executing a custom SQL assertion
  """
  CUSTOM_SQL_ERROR

  """
  Error while executing a field assertion
  """
  FIELD_ASSERTION_ERROR

  """
  Unknown error
  """
  UNKNOWN_ERROR
}

type BatchSpec {
  """
  The native identifier as specified by the system operating on the batch.
  """
  nativeBatchId: String

  """
  A query that identifies a batch of data
  """
  query: String

  """
  Any limit to the number of rows in the batch, if applied
  """
  limit: Int

  """
  Custom properties of the Batch
  """
  customProperties: [StringMapEntry!]
}

"""
The result type of an assertion, success or failure.
"""
enum AssertionResultType {
  """
  The assertion has not yet been fully evaluated.
  """
  INIT

  """
  The assertion succeeded.
  """
  SUCCESS

  """
  The assertion failed.
  """
  FAILURE

  """
  The assertion errored.
  """
  ERROR
}

"""
The state of an assertion run, as defined within an Assertion Run Event.
"""
enum AssertionRunStatus {
  """
  An assertion run has completed.
  """
  COMPLETE
}

"""
An "aggregation" function that can be applied to column values of a Dataset to create the input to an Assertion Operator.
"""
enum AssertionStdAggregation {
  """
  Assertion is applied on individual column value
  """
  IDENTITY

  """
  Assertion is applied on column mean
  """
  MEAN

  """
  Assertion is applied on column median
  """
  MEDIAN

  """
  Assertion is applied on number of distinct values in column
  """
  UNIQUE_COUNT

  """
  Assertion is applied on proportion of distinct values in column
  """
  UNIQUE_PROPOTION

  """
  Assertion is applied on number of null values in column
  """
  NULL_COUNT

  """
  Assertion is applied on proportion of null values in column
  """
  NULL_PROPORTION

  """
  Assertion is applied on column std deviation
  """
  STDDEV

  """
  Assertion is applied on column min
  """
  MIN

  """
  Assertion is applied on column std deviation
  """
  MAX

  """
  Assertion is applied on column sum
  """
  SUM

  """
  Assertion is applied on all columns
  """
  COLUMNS

  """
  Assertion is applied on number of columns
  """
  COLUMN_COUNT

  """
  Assertion is applied on number of rows
  """
  ROW_COUNT

  """
  Other
  """
  _NATIVE_
}

"""
A standard operator or condition that constitutes an assertion definition
"""
enum AssertionStdOperator {
  """
  Value being asserted is between min_value and max_value
  """
  BETWEEN

  """
  Value being asserted is less than max_value
  """
  LESS_THAN

  """
  Value being asserted is less than or equal to max_value
  """
  LESS_THAN_OR_EQUAL_TO

  """
  Value being asserted is greater than min_value
  """
  GREATER_THAN

  """
  Value being asserted is greater than or equal to min_value
  """
  GREATER_THAN_OR_EQUAL_TO

  """
  Value being asserted is equal to value
  """
  EQUAL_TO

  """
  Value being asserted is not equal to value
  """
  NOT_EQUAL_TO

  """
  Value being asserted is null
  """
  NULL

  """
  Value being asserted is not null
  """
  NOT_NULL

  """
  Value being asserted contains value
  """
  CONTAIN

  """
  Value being asserted ends with value
  """
  END_WITH

  """
  Value being asserted starts with value
  """
  START_WITH

  """
  Value being asserted matches the regex value.
  """
  REGEX_MATCH

  """
  Value being asserted is one of the array values
  """
  IN

  """
  Value being asserted is not in one of the array values.
  """
  NOT_IN

  """
  Value being asserted is true
  """
  IS_TRUE

  """
  Value being asserted is false
  """
  IS_FALSE

  """
  Other
  """
  _NATIVE_
}

"""
Parameters for AssertionStdOperators
"""
type AssertionStdParameters {
  """
  The value parameter of an assertion
  """
  value: AssertionStdParameter

  """
  The maxValue parameter of an assertion
  """
  maxValue: AssertionStdParameter

  """
  The minValue parameter of an assertion
  """
  minValue: AssertionStdParameter
}

"""
Parameter for AssertionStdOperator.
"""
type AssertionStdParameter {
  """
  The parameter value
  """
  value: String!

  """
  The type of the parameter
  """
  type: AssertionStdParameterType!
}

"""
The type of an AssertionStdParameter
"""
enum AssertionStdParameterType {
  """
  A string value
  """
  STRING

  """
  A numeric value
  """
  NUMBER

  """
  A list of values. When used, the value should be formatted as a serialized JSON array.
  """
  LIST

  """
  A set of values. When used, the value should be formatted as a serialized JSON array.
  """
  SET

  """
  A value of unknown type
  """
  UNKNOWN
}

"""
The scope that a Dataset-level assertion applies to.
"""
enum DatasetAssertionScope {
  """
  Assertion applies to columns of a dataset.
  """
  DATASET_COLUMN

  """
  Assertion applies to rows of a dataset.
  """
  DATASET_ROWS

  """
  Assertion applies to schema of a dataset.
  """
  DATASET_SCHEMA

  """
  The scope of an assertion is unknown.
  """
  UNKNOWN
}

"""
The top-level assertion type.
"""
enum AssertionType {
  """
  A single-dataset assertion.
  """
  DATASET
  """
  An assertion which indicates when a particular operation should occur to an asset.
  """
  FRESHNESS
  """
  An assertion which indicates how much data should be available for a particular asset.
  """
  VOLUME
  """
  A raw SQL-statement based assertion.
  """
  SQL
  """
  A structured assertion targeting a specific column or field of the Dataset.
  """
  FIELD
  """
  A schema or structural assertion.
  """
  DATA_SCHEMA

  """
  A custom assertion.
  """
  CUSTOM
}

"""
Result returned when fetching run events for an assertion.
"""
type AssertionRunEventsResult {
  """
  The total number of run events returned
  """
  total: Int!

  """
  The number of failed run events
  """
  failed: Int!

  """
  The number of succeeded run events
  """
  succeeded: Int!

  """
  The number of errored run events
  """
  errored: Int!

  """
  The run events themselves
  """
  runEvents: [AssertionRunEvent!]!
}

"""
A list of Assertions Associated with an Entity
"""
type EntityAssertionsResult {
  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of assertions in the returned result set
  """
  count: Int!

  """
  The total number of assertions in the result set
  """
  total: Int!

  """
  The assertions themselves
  """
  assertions: [Assertion!]!
}

"""
Operational info for an entity.
"""
type Operation implements TimeSeriesAspect {
  """
  The time at which the operation was reported
  """
  timestampMillis: Long!

  """
  Actor who issued this operation.
  """
  actor: String

  """
  Operation type of change.
  """
  operationType: OperationType!

  """
  A custom operation type
  """
  customOperationType: String

  """
  Source of the operation
  """
  sourceType: OperationSourceType

  """
  How many rows were affected by this operation.
  """
  numAffectedRows: Long

  """
  Which other datasets were affected by this operation.
  """
  affectedDatasets: [String!]

  """
  When time at which the asset was actually updated
  """
  lastUpdatedTimestamp: Long!

  """
  Optional partition identifier
  """
  partition: String

  """
  Custom operation properties
  """
  customProperties: [StringMapEntry!]
}

"""
Enum to define the operation type when an entity changes.
"""
enum OperationType {
  """
  When data is inserted.
  """
  INSERT

  """
  When data is updated.
  """
  UPDATE

  """
  When data is deleted.
  """
  DELETE

  """
  When table is created.
  """
  CREATE

  """
  When table is altered
  """
  ALTER

  """
  When table is dropped
  """
  DROP

  """
  Unknown operation
  """
  UNKNOWN

  """
  Custom
  """
  CUSTOM
}

"""
Input provided to report an asset operation
"""
input ReportOperationInput {
  """
  The urn of the asset (e.g. dataset) to report the operation for
  """
  urn: String!

  """
  The type of operation that was performed. Required
  """
  operationType: OperationType!

  """
  A custom type of operation. Required if operation type is CUSTOM.
  """
  customOperationType: String

  """
  The source or reporter of the operation
  """
  sourceType: OperationSourceType!

  """
  A list of key-value parameters to include
  """
  customProperties: [StringMapEntryInput!]

  """
  An optional partition identifier
  """
  partition: String

  """
  Optional: The number of affected rows
  """
  numAffectedRows: Long

  """
  Optional: Provide a timestamp associated with the operation. If not provided, one will be generated for you based
  on the current time.
  """
  timestampMillis: Long
}

"""
Enum to define the source/reporter type for an Operation.
"""
enum OperationSourceType {
  """
  A data process reported the operation.
  """
  DATA_PROCESS

  """
  A data platform reported the operation.
  """
  DATA_PLATFORM
}

"""
Information about Metadata Entity deprecation status
"""
type Deprecation {
  """
  Whether the entity has been deprecated by owner
  """
  deprecated: Boolean!

  """
  The time user plan to decommission this entity
  """
  decommissionTime: Long

  """
  Additional information about the entity deprecation plan
  """
  note: String

  """
  The user who will be credited for modifying this deprecation content
  """
  actor: String

  """
  The hydrated user who will be credited for modifying this deprecation content
  """
  actorEntity: Entity

  """
  The optional replacement entity
  """
  replacement: Entity
}

"""
Input provided when updating the association between a Metadata Entity and a Glossary Term
"""
input TermAssociationInput {
  """
  The primary key of the Glossary Term to add or remove
  """
  termUrn: String!

  """
  The target Metadata Entity to add or remove the Glossary Term from
  """
  resourceUrn: String!

  """
  An optional type of a sub resource to attach the Glossary Term to
  """
  subResourceType: SubResourceType

  """
  An optional sub resource identifier to attach the Glossary Term to
  """
  subResource: String
}

"""
Input provided when adding Terms to an asset
"""
input AddTermsInput {
  """
  The primary key of the Glossary Term to add or remove
  """
  termUrns: [String!]!

  """
  The target Metadata Entity to add or remove the Glossary Term from
  """
  resourceUrn: String!

  """
  An optional type of a sub resource to attach the Glossary Term to
  """
  subResourceType: SubResourceType

  """
  An optional sub resource identifier to attach the Glossary Term to
  """
  subResource: String
}

"""
A type of Metadata Entity sub resource
"""
enum SubResourceType {
  """
  A Dataset field or column
  """
  DATASET_FIELD
}

"""
Input provided when adding Terms to an asset
"""
input RelatedTermsInput {
  """
  The Glossary Term urn to add or remove this relationship to/from
  """
  urn: String!

  """
  The primary key of the Glossary Term to add or remove
  """
  termUrns: [String!]!

  """
  The type of relationship we're adding or removing to/from for a Glossary Term
  """
  relationshipType: TermRelationshipType!
}

"""
A type of Metadata Entity sub resource
"""
enum TermRelationshipType {
  """
  When a Term inherits from, or has an 'Is A' relationship with another Term
  """
  isA

  """
  When a Term contains, or has a 'Has A' relationship with another Term
  """
  hasA
}

"""
Input provided when adding glossary terms to a batch of assets
"""
input BatchAddTermsInput {
  """
  The primary key of the Glossary Terms
  """
  termUrns: [String!]!

  """
  The target assets to attach the glossary terms to
  """
  resources: [ResourceRefInput]!
}

"""
Input provided when removing glossary terms from a batch of assets
"""
input BatchRemoveTermsInput {
  """
  The primary key of the Glossary Terms
  """
  termUrns: [String!]!

  """
  The target assets to remove the glossary terms from
  """
  resources: [ResourceRefInput]!
}

"""
Input provided when updating the association between a Metadata Entity and a Tag
"""
input TagAssociationInput {
  """
  The primary key of the Tag to add or remove
  """
  tagUrn: String!

  """
  The target Metadata Entity to add or remove the Tag to
  """
  resourceUrn: String!

  """
  An optional type of a sub resource to attach the Tag to
  """
  subResourceType: SubResourceType

  """
  An optional sub resource identifier to attach the Tag to
  """
  subResource: String
}

"""
Input provided when adding tags to an asset
"""
input AddTagsInput {
  """
  The primary key of the Tags
  """
  tagUrns: [String!]!

  """
  The target Metadata Entity to add or remove the Tag to
  """
  resourceUrn: String!

  """
  An optional type of a sub resource to attach the Tag to
  """
  subResourceType: SubResourceType

  """
  An optional sub resource identifier to attach the Tag to
  """
  subResource: String
}

"""
Input provided when adding tags to a batch of assets
"""
input BatchAddTagsInput {
  """
  The primary key of the Tags
  """
  tagUrns: [String!]!

  """
  The target assets to attach the tags to
  """
  resources: [ResourceRefInput!]!
}

"""
Input provided when removing tags from a batch of assets
"""
input BatchRemoveTagsInput {
  """
  The primary key of the Tags
  """
  tagUrns: [String!]!

  """
  The target assets to remove the tags from
  """
  resources: [ResourceRefInput]!
}

"""
Reference to a resource to apply an action to
"""
input ResourceRefInput {
  """
  The urn of the resource being referenced
  """
  resourceUrn: String!

  """
  An optional type of a sub resource to attach the Tag to
  """
  subResourceType: SubResourceType

  """
  An optional sub resource identifier to attach the Tag to
  """
  subResource: String
}

"""
Entities that are able to own other entities
"""
enum OwnerEntityType {
  """
  A corp user owner
  """
  CORP_USER

  """
  A corp group owner
  """
  CORP_GROUP
}

"""
Input provided when adding the association between a Metadata Entity and an user or group owner
"""
input AddOwnerInput {
  """
  The primary key of the Owner to add or remove
  """
  ownerUrn: String!

  """
  The owner type, either a user or group
  """
  ownerEntityType: OwnerEntityType!

  """
  The ownership type for the new owner. If none is provided, then a new NONE will be added.
  Deprecated - Use ownershipTypeUrn field instead.
  """
  type: OwnershipType @deprecated

  """
  The urn of the ownership type entity.
  """
  ownershipTypeUrn: String

  """
  The urn of the resource or entity to attach or remove the owner from, for example a dataset urn
  """
  resourceUrn: String!
}

"""
Input provided when adding an owner to an asset
"""
input OwnerInput {
  """
  The primary key of the Owner to add or remove
  """
  ownerUrn: String!

  """
  The owner type, either a user or group
  """
  ownerEntityType: OwnerEntityType!

  """
  The ownership type for the new owner. If none is provided, then a new NONE will be added.
  Deprecated - Use ownershipTypeUrn field instead.
  """
  type: OwnershipType @deprecated

  """
  The urn of the ownership type entity.
  """
  ownershipTypeUrn: String
}

"""
Input provided when adding multiple associations between a Metadata Entity and an user or group owner
"""
input AddOwnersInput {
  """
  The primary key of the Owner to add or remove
  """
  owners: [OwnerInput!]!

  """
  The urn of the resource or entity to attach or remove the owner from, for example a dataset urn
  """
  resourceUrn: String!
}

"""
Input provided when adding owners to a batch of assets
"""
input BatchAddOwnersInput {
  """
  The primary key of the owners
  """
  owners: [OwnerInput!]!

  """
  The ownership type to remove, optional. By default will remove regardless of ownership type.
  """
  ownershipTypeUrn: String

  """
  The target assets to attach the owners to
  """
  resources: [ResourceRefInput]!
}

"""
Input provided when removing owners from a batch of assets
"""
input BatchRemoveOwnersInput {
  """
  The primary key of the owners
  """
  ownerUrns: [String!]!

  """
  The ownership type to remove, optional. By default will remove regardless of ownership type.
  """
  ownershipTypeUrn: String

  """
  The target assets to remove the owners from
  """
  resources: [ResourceRefInput]!
}

"""
Input provided when removing the association between a Metadata Entity and an user or group owner
"""
input RemoveOwnerInput {
  """
  The primary key of the Owner to add or remove
  """
  ownerUrn: String!

  """
  The ownership type to remove, optional. By default will remove regardless of ownership type.
  """
  ownershipTypeUrn: String

  """
  The urn of the resource or entity to attach or remove the owner from, for example a dataset urn
  """
  resourceUrn: String!
}

"""
Input provided when adding the association between a Metadata Entity and a Link
"""
input AddLinkInput {
  """
  The url of the link to add or remove
  """
  linkUrl: String!

  """
  A label to attach to the link
  """
  label: String!

  """
  The urn of the resource or entity to attach the link to, for example a dataset urn
  """
  resourceUrn: String!
}

"""
Input provided when removing the association between a Metadata Entity and a Link
"""
input RemoveLinkInput {
  """
  The url of the link to add or remove, which uniquely identifies the Link
  """
  linkUrl: String!

  """
  The urn of the resource or entity to attach the link to, for example a dataset urn
  """
  resourceUrn: String!
}

"""
Incubating. Updates the description of a resource. Currently supports DatasetField descriptions only
"""
input DescriptionUpdateInput {
  """
  The new description
  """
  description: String!

  """
  The primary key of the resource to attach the description to, eg dataset urn
  """
  resourceUrn: String!

  """
  An optional sub resource type
  """
  subResourceType: SubResourceType

  """
  A sub resource identifier, eg dataset field path
  """
  subResource: String
}

"""
Input for updating the parent node of a resource. Currently only GlossaryNodes and GlossaryTerms have parentNodes.
"""
input UpdateParentNodeInput {
  """
  The new parent node urn. If parentNode is null, this will remove the parent from this entity
  """
  parentNode: String

  """
  The primary key of the resource to update the parent node for
  """
  resourceUrn: String!
}

"""
Input for updating the parent domain of a domain.
"""
input MoveDomainInput {
  """
  The new parent domain urn. If parentDomain is null, this will remove the parent from this entity
  """
  parentDomain: String

  """
  The primary key of the resource to update the parent domain for
  """
  resourceUrn: String!
}

"""
Input for updating the name of an entity
"""
input UpdateNameInput {
  """
  The new name
  """
  name: String!

  """
  The primary key of the resource to update the name for
  """
  urn: String!
}

"""
Input provided when setting the Deprecation status for an Entity.
"""
input UpdateDeprecationInput {
  """
  The urn of the Entity to set deprecation for.
  """
  urn: String!

  """
  An optional type of a sub resource to set the deprecation for
  """
  subResourceType: SubResourceType

  """
  An optional sub resource identifier to set the deprecation for
  """
  subResource: String

  """
  Whether the Entity is marked as deprecated.
  """
  deprecated: Boolean!

  """
  Optional - The time user plan to decommission this entity
  """
  decommissionTime: Long

  """
  Optional - Additional information about the entity deprecation plan
  """
  note: String

  """
  Optional - URN to replace the entity with
  """
  replacement: String
}

"""
Input provided when updating the deprecation status for a batch of assets.
"""
input BatchUpdateDeprecationInput {
  """
  Whether the Entity is marked as deprecated.
  """
  deprecated: Boolean!

  """
  Optional - The time user plan to decommission this entity
  """
  decommissionTime: Long

  """
  Optional - Additional information about the entity deprecation plan
  """
  note: String

  """
  The target assets to attach the tags to
  """
  resources: [ResourceRefInput]!

  """
  Optional - URN to replace the entity with
  """
  replacement: String
}

"""
Input provided when adding tags to a batch of assets
"""
input BatchSetDomainInput {
  """
  The primary key of the Domain, or null if the domain will be unset
  """
  domainUrn: String

  """
  The target assets to attach the Domain
  """
  resources: [ResourceRefInput!]!
}

"""
Input provided when creating or updating an Access Policy
"""
input PolicyUpdateInput {
  """
  The Policy Type
  """
  type: PolicyType!

  """
  The Policy name
  """
  name: String!

  """
  The Policy state
  """
  state: PolicyState!

  """
  A Policy description
  """
  description: String

  """
  The set of resources that the Policy privileges apply to
  """
  resources: ResourceFilterInput

  """
  The set of privileges that the Policy grants
  """
  privileges: [String!]!

  """
  The set of actors that the Policy privileges are granted to
  """
  actors: ActorFilterInput!
}

"""
Input required to add members to a native DataHub group
"""
input AddNativeGroupMembersInput {
  """
  The group to add members to
  """
  groupUrn: String!

  """
  The members to add to the group
  """
  userUrns: [String!]!
}

"""
Input required to add members to an external DataHub group
"""
input AddGroupMembersInput {
  """
  The group to add members to
  """
  groupUrn: String!

  """
  The members to add to the group
  """
  userUrns: [String!]!
}

"""
Input required to remove members from a native DataHub group
"""
input RemoveNativeGroupMembersInput {
  """
  The group to remove members from
  """
  groupUrn: String!

  """
  The members to remove from the group
  """
  userUrns: [String!]!
}

"""
Input required to remove members from an external DataHub group
"""
input RemoveGroupMembersInput {
  """
  The group to remove members from
  """
  groupUrn: String!

  """
  The members to remove from the group
  """
  userUrns: [String!]!
}

"""
The type of the Access Policy
"""
enum PolicyType {
  """
  An access policy that grants privileges pertaining to Metadata Entities
  """
  METADATA

  """
  An access policy that grants top level administrative privileges pertaining to the DataHub Platform itself
  """
  PLATFORM
}

"""
The state of an Access Policy
"""
enum PolicyState {
  """
  A Policy that has not been officially created, but in progress
  Currently unused
  """
  DRAFT

  """
  A Policy that is active and being enforced
  """
  ACTIVE

  """
  A Policy that is not active or being enforced
  """
  INACTIVE
}

"""
DEPRECATED
TODO: Eventually get rid of this in favor of DataHub Policy
An DataHub Platform Access Policy Access Policies determine who can perform what actions against which resources on the platform
"""
type Policy {
  """
  The primary key of the Policy
  """
  urn: String!

  """
  The type of the Policy
  """
  type: PolicyType!

  """
  The name of the Policy
  """
  name: String!

  """
  The present state of the Policy
  """
  state: PolicyState!

  """
  The description of the Policy
  """
  description: String

  """
  The resources that the Policy privileges apply to
  """
  resources: ResourceFilter

  """
  The privileges that the Policy grants
  """
  privileges: [String!]!

  """
  The actors that the Policy grants privileges to
  """
  actors: ActorFilter!

  """
  Whether the Policy is editable, ie system policies, or not
  """
  editable: Boolean!
}

"""
An DataHub Platform Access Policy -  Policies determine who can perform what actions against which resources on the platform
"""
type DataHubPolicy implements Entity {
  """
  The primary key of the Policy
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  Granular API for querying edges extending from the Role
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  The type of the Policy
  """
  policyType: PolicyType!

  """
  The name of the Policy
  """
  name: String!

  """
  The present state of the Policy
  """
  state: PolicyState!

  """
  The description of the Policy
  """
  description: String

  """
  The resources that the Policy privileges apply to
  """
  resources: ResourceFilter

  """
  The privileges that the Policy grants
  """
  privileges: [String!]!

  """
  The actors that the Policy grants privileges to
  """
  actors: ActorFilter!

  """
  Whether the Policy is editable, ie system policies, or not
  """
  editable: Boolean!
}

"""
The resources that a DataHub Access Policy applies to
"""
type ResourceFilter {
  """
  Deprecated, use filter instead
  The type of the resource the policy should apply to Not required because in the future we want to support filtering by type OR by domain
  """
  type: String @deprecated

  """
  Deprecated, use filter instead
  A list of specific resource urns to apply the filter to
  """
  resources: [String!] @deprecated

  """
  Deprecated, use filter instead
  Whether of not to apply the filter to all resources of the type
  """
  allResources: Boolean @deprecated

  """
  Whether of not to apply the filter to all resources of the type
  """
  filter: PolicyMatchFilter

  """
  Constraints on what subresources can be acted upon
  """
  privilegeConstraints: PolicyMatchFilter
}

"""
The actors that a DataHub Access Policy applies to
"""
type ActorFilter {
  """
  A disjunctive set of users to apply the policy to
  """
  users: [String!]

  """
  A disjunctive set of groups to apply the policy to
  """
  groups: [String!]

  """
  A disjunctive set of roles to apply the policy to
  """
  roles: [String!]

  """
  Whether the filter should return TRUE for owners of a particular resource
  Only applies to policies of type METADATA, which have a resource associated with them
  """
  resourceOwners: Boolean!

  """
  Set of OwnershipTypes to apply the policy to (if resourceOwners field is set to True)
  """
  resourceOwnersTypes: [String!]

  """
  Set of OwnershipTypes to apply the policy to (if resourceOwners field is set to True), resolved.
  """
  resolvedOwnershipTypes: [OwnershipTypeEntity!]

  """
  Whether the filter should apply to all users
  """
  allUsers: Boolean!

  """
  Whether the filter should apply to all groups
  """
  allGroups: Boolean!

  """
  The list of users on the Policy, resolved.
  """
  resolvedUsers: [CorpUser!]

  """
  The list of groups on the Policy, resolved.
  """
  resolvedGroups: [CorpGroup!]

  """
  The list of roles on the Policy, resolved.
  """
  resolvedRoles: [DataHubRole!]
}

"""
Filter object that encodes a complex filter logic with OR + AND
"""
type PolicyMatchFilter {
  """
  List of criteria to apply
  """
  criteria: [PolicyMatchCriterion!]
}

"""
Criterion to define relationship between field and values
"""
type PolicyMatchCriterion {
  """
  The name of the field that the criterion refers to
  e.g. entity_type, entity_urn, domain
  """
  field: String!

  """
  Values. Matches criterion if any one of the values matches condition (OR-relationship)
  """
  values: [PolicyMatchCriterionValue!]!

  """
  The name of the field that the criterion refers to
  """
  condition: PolicyMatchCondition!
}

"""
Value in PolicyMatchCriterion with hydrated entity if value is urn
"""
type PolicyMatchCriterionValue {
  """
  The value of the field to match
  """
  value: String!

  """
  Hydrated entities of the above values. Only set if the value is an urn
  """
  entity: Entity
}

"""
Match condition
"""
enum PolicyMatchCondition {
  """
  Whether the field matches the value
  """
  EQUALS
  """
  Whether the field value starts with the value
  """
  STARTS_WITH
  """
  Whether the field does not match the value
  """
  NOT_EQUALS
}

"""
Input required when creating or updating an Access Policies Determines which resources the Policy applies to
"""
input ResourceFilterInput {
  """
  Deprecated, use filter field instead
  The type of the resource the policy should apply to
  Not required because in the future we want to support filtering by type OR by domain
  """
  type: String @deprecated

  """
  Deprecated, use filter instead
  A list of specific resource urns to apply the filter to
  """
  resources: [String!]

  """
  Deprecated, use empty filter instead
  Whether of not to apply the filter to all resources of the type
  """
  allResources: Boolean

  """
  Whether of not to apply the filter to all resources of the type
  """
  filter: PolicyMatchFilterInput

  """
  Constraints on what subresources can be acted upon
  """
  privilegeConstraints: PolicyMatchFilterInput
}

"""
Input required when creating or updating an Access Policies Determines which actors the Policy applies to
"""
input ActorFilterInput {
  """
  A disjunctive set of users to apply the policy to
  """
  users: [String!]

  """
  A disjunctive set of groups to apply the policy to
  """
  groups: [String!]

  """
  Whether the filter should return TRUE for owners of a particular resource
  Only applies to policies of type METADATA, which have a resource associated with them
  """
  resourceOwners: Boolean!

  """
  Set of OwnershipTypes to apply the policy to (if resourceOwners field is set to True)
  """
  resourceOwnersTypes: [String!]

  """
  Whether the filter should apply to all users
  """
  allUsers: Boolean!

  """
  Whether the filter should apply to all groups
  """
  allGroups: Boolean!
}

"""
Filter object that encodes a complex filter logic with OR + AND
"""
input PolicyMatchFilterInput {
  """
  List of criteria to apply
  """
  criteria: [PolicyMatchCriterionInput!]
}

"""
Criterion to define relationship between field and values
"""
input PolicyMatchCriterionInput {
  """
  The name of the field that the criterion refers to
  e.g. entity_type, entity_urn, domain
  """
  field: String!

  """
  Values. Matches criterion if any one of the values matches condition (OR-relationship)
  """
  values: [String!]!

  """
  The name of the field that the criterion refers to
  """
  condition: PolicyMatchCondition!
}

"""
Input required when listing DataHub Access Policies
"""
input ListPoliciesInput {
  """
  The starting offset of the result set returned
  """
  start: Int

  """
  The maximum number of Policies to be returned in the result set
  """
  count: Int

  """
  Optional search query
  """
  query: String

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]
}

"""
The result obtained when listing DataHub Access Policies
"""
type ListPoliciesResult {
  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of Policies in the returned result set
  """
  count: Int!

  """
  The total number of Policies in the result set
  """
  total: Int!

  """
  The Policies themselves
  """
  policies: [Policy!]!
}

"""
Input for getting granted privileges
"""
input GetGrantedPrivilegesInput {
  """
  Urn of the actor
  """
  actorUrn: String!

  """
  Spec to identify resource. If empty, gets privileges granted to the actor
  """
  resourceSpec: ResourceSpec

  """
  Whether to include policy evaluation details.
  This will only return result if user has Manage Policies Privilege.
  """
  includeEvaluationDetails: Boolean = false
}

"""
Spec to identify resource
"""
input ResourceSpec {
  """
  Resource type
  """
  resourceType: EntityType!

  """
  Resource urn
  """
  resourceUrn: String!
}

"""
Object that encodes the privileges the actor has for a given resource
"""
type Privileges {
  """
  Granted Privileges
  """
  privileges: [String!]!

  """
  Details about how each policy was evaluated
  """
  evaluationDetails: [PolicyEvaluationDetail]
}

"""
Input required when listing DataHub Users
"""
input ListUsersInput {
  """
  The starting offset of the result set returned
  """
  start: Int

  """
  The maximum number of Policies to be returned in the result set
  """
  count: Int

  """
  Optional search query
  """
  query: String
}

"""
The result obtained when listing DataHub Users
"""
type ListUsersResult {
  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of Policies in the returned result set
  """
  count: Int!

  """
  The total number of Policies in the result set
  """
  total: Int!

  """
  The users themselves
  """
  users: [CorpUser!]!
}

"""
Input required when listing DataHub Groups
"""
input ListGroupsInput {
  """
  The starting offset of the result set returned
  """
  start: Int

  """
  The maximum number of Policies to be returned in the result set
  """
  count: Int

  """
  Optional search query
  """
  query: String
}

type EntityCountResults {
  counts: [EntityCountResult!]
}

type EntityCountResult {
  entityType: EntityType!
  count: Int!
}

"""
The result obtained when listing DataHub Groups
"""
type ListGroupsResult {
  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of Policies in the returned result set
  """
  count: Int!

  """
  The total number of Policies in the result set
  """
  total: Int!

  """
  The groups themselves
  """
  groups: [CorpGroup!]!
}

"""
A time stamp along with an optional actor
"""
type AuditStamp {
  """
  When the audited action took place
  """
  time: Long!

  """
  Who performed the audited action
  """
  actor: String
}

"""
Input for creating a new group
"""
input CreateGroupInput {
  """
  Optional! A custom id to use as the primary key identifier for the group. If not provided, a random UUID will be generated as the id.
  """
  id: String

  """
  The display name of the group
  """
  name: String!

  """
  The description of the group
  """
  description: String
}

"""
An ML Model Metadata Entity Note that this entity is incubating
"""
type MLModel implements EntityWithRelationships & Entity & BrowsableEntity {
  """
  The primary key of the ML model
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  ML model display name
  """
  name: String!

  """
  Standardized platform urn where the MLModel is defined
  """
  platform: DataPlatform!

  """
  Fabric type where mlmodel belongs to or where it was generated
  """
  origin: FabricType!

  """
  Human readable description for mlmodel
  """
  description: String

  """
  Deprecated, use tags field instead
  The standard tags for the ML Model
  """
  globalTags: GlobalTags @deprecated

  """
  The standard tags for the ML Model
  """
  tags: GlobalTags

  """
  Ownership metadata of the mlmodel
  """
  ownership: Ownership

  """
  Additional read only information about the ML Model
  """
  properties: MLModelProperties

  """
  Intended use of the mlmodel
  """
  intendedUse: IntendedUse

  """
  Factors metadata of the mlmodel
  """
  factorPrompts: MLModelFactorPrompts

  """
  Metrics metadata of the mlmodel
  """
  metrics: Metrics

  """
  Evaluation Data of the mlmodel
  """
  evaluationData: [BaseData!]

  """
  Training Data of the mlmodel
  """
  trainingData: [BaseData!]

  """
  Quantitative Analyses of the mlmodel
  """
  quantitativeAnalyses: QuantitativeAnalyses

  """
  Ethical Considerations of the mlmodel
  """
  ethicalConsiderations: EthicalConsiderations

  """
  Caveats and Recommendations of the mlmodel
  """
  caveatsAndRecommendations: CaveatsAndRecommendations

  """
  References to internal resources related to the mlmodel
  """
  institutionalMemory: InstitutionalMemory

  """
  Source Code
  """
  sourceCode: SourceCode

  """
  Status metadata of the mlmodel
  """
  status: Status

  """
  Cost Aspect of the mlmodel
  """
  cost: Cost

  """
  Deprecation
  """
  deprecation: Deprecation

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  The browse paths corresponding to the ML Model. If no Browse Paths have been generated before, this will be null.
  """
  browsePaths: [BrowsePath!]

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2

  """
  The structured glossary terms associated with the entity
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the entity
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  An additional set of of read write properties
  """
  editableProperties: MLModelEditableProperties

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges
}

"""
An ML Model Group Metadata Entity
Note that this entity is incubating
"""
type MLModelGroup implements EntityWithRelationships & Entity & BrowsableEntity {
  """
  The primary key of the ML Model Group
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  The display name for the Entity
  """
  name: String!

  """
  Standardized platform urn where the MLModelGroup is defined
  """
  platform: DataPlatform!

  """
  Fabric type where MLModelGroup belongs to or where it was generated
  """
  origin: FabricType!

  """
  Human readable description for MLModelGroup
  """
  description: String

  """
  Additional read only properties about the ML Model Group
  """
  properties: MLModelGroupProperties

  """
  Ownership metadata of the MLModelGroup
  """
  ownership: Ownership

  """
  Status metadata of the MLModelGroup
  """
  status: Status

  """
  Deprecation
  """
  deprecation: Deprecation

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  The browse paths corresponding to the ML Model Group. If no Browse Paths have been generated before, this will be null.
  """
  browsePaths: [BrowsePath!]

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2

  """
  Tags applied to entity
  """
  tags: GlobalTags

  """
  The structured glossary terms associated with the entity
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the entity
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  An additional set of of read write properties
  """
  editableProperties: MLModelGroupEditableProperties

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges
}

"""
Properties describing a group of related ML models
"""
type MLModelGroupProperties {
  """
  Display name of the model group
  """
  name: String

  """
  Detailed description of the model group's purpose and contents
  """
  description: String

  """
  When this model group was created
  """
  created: AuditStamp

  """
  When this model group was last modified
  """
  lastModified: AuditStamp

  """
  Version identifier for this model group
  """
  version: VersionTag

  """
  Custom key-value properties for the model group
  """
  customProperties: [CustomPropertiesEntry!]

  """
  URL to view this model group in the external system
  """
  externalUrl: String

  """
  Deprecated creation timestamp
  @deprecated Use the 'created' field instead
  """
  createdAt: Long @deprecated(reason: "Use `created` instead")
}

"""
An ML Feature Metadata Entity Note that this entity is incubating
"""
type MLFeature implements EntityWithRelationships & Entity {
  """
  The primary key of the ML Feature
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  The display name for the ML Feature
  """
  name: String!

  """
  MLFeature featureNamespace
  """
  featureNamespace: String!

  """
  The description about the ML Feature
  """
  description: String

  """
  MLFeature data type
  """
  dataType: MLFeatureDataType

  """
  Ownership metadata of the MLFeature
  """
  ownership: Ownership

  """
  ModelProperties metadata of the MLFeature
  """
  featureProperties: MLFeatureProperties @deprecated

  """
  ModelProperties metadata of the MLFeature
  """
  properties: MLFeatureProperties

  """
  References to internal resources related to the MLFeature
  """
  institutionalMemory: InstitutionalMemory

  """
  Status metadata of the MLFeature
  """
  status: Status

  """
  Deprecation
  """
  deprecation: Deprecation

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  Tags applied to entity
  """
  tags: GlobalTags

  """
  The structured glossary terms associated with the entity
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the entity
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  An additional set of of read write properties
  """
  editableProperties: MLFeatureEditableProperties

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges
}

type MLHyperParam {
  name: String

  description: String

  value: String

  createdAt: Long
}

type MLMetric {
  """
  Name of the metric (e.g. accuracy, precision, recall)
  """
  name: String

  """
  Description of what this metric measures
  """
  description: String

  """
  The computed value of the metric
  """
  value: String

  """
  Timestamp when this metric was recorded
  """
  createdAt: Long
}

type MLModelProperties {
  """
  The display name of the model used in the UI
  """
  name: String

  """
  Detailed description of the model's purpose and characteristics
  """
  description: String

  """
  When the model was last modified
  """
  lastModified: AuditStamp

  """
  Version identifier for this model
  """
  version: String

  """
  The type/category of ML model (e.g. classification, regression)
  """
  type: String

  """
  Mapping of hyperparameter configurations
  """
  hyperParameters: HyperParameterMap

  """
  List of hyperparameter settings used to train this model
  """
  hyperParams: [MLHyperParam]

  """
  Performance metrics from model training
  """
  trainingMetrics: [MLMetric]

  """
  Names of ML features used by this model
  """
  mlFeatures: [String!]

  """
  Tags for categorizing and searching models
  """
  tags: [String!]

  """
  Model groups this model belongs to
  """
  groups: [MLModelGroup]

  """
  Additional custom properties specific to this model
  """
  customProperties: [CustomPropertiesEntry!]

  """
  URL to view this model in external system
  """
  externalUrl: String

  """
  When this model was created
  """
  created: AuditStamp

  """
  Deprecated timestamp for model creation
  @deprecated Use 'created' field instead
  """
  date: Long @deprecated(reason: "Use `created` instead")
}

type MLFeatureProperties {
  description: String

  dataType: MLFeatureDataType

  version: VersionTag

  sources: [Dataset]

  customProperties: [CustomPropertiesEntry!]
}

"""
An ML Primary Key Entity Note that this entity is incubating
"""
type MLPrimaryKey implements EntityWithRelationships & Entity {
  """
  The primary key of the ML Primary Key
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  The display name
  """
  name: String!

  """
  MLPrimaryKey featureNamespace
  """
  featureNamespace: String!

  """
  MLPrimaryKey description
  """
  description: String

  """
  MLPrimaryKey data type
  """
  dataType: MLFeatureDataType

  """
  Additional read only properties of the ML Primary Key
  """
  properties: MLPrimaryKeyProperties

  """
  Deprecated, use properties field instead
  MLPrimaryKeyProperties
  """
  primaryKeyProperties: MLPrimaryKeyProperties @deprecated

  """
  Ownership metadata of the MLPrimaryKey
  """
  ownership: Ownership

  """
  References to internal resources related to the MLPrimaryKey
  """
  institutionalMemory: InstitutionalMemory

  """
  Status metadata of the MLPrimaryKey
  """
  status: Status

  """
  Deprecation
  """
  deprecation: Deprecation

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  Tags applied to entity
  """
  tags: GlobalTags

  """
  The structured glossary terms associated with the entity
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the entity
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  An additional set of of read write properties
  """
  editableProperties: MLPrimaryKeyEditableProperties

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges
}

type MLPrimaryKeyProperties {
  description: String

  dataType: MLFeatureDataType

  version: VersionTag

  sources: [Dataset]

  customProperties: [CustomPropertiesEntry!]
}

"""
An ML Feature Table Entity Note that this entity is incubating
"""
type MLFeatureTable implements EntityWithRelationships & Entity & BrowsableEntity {
  """
  The primary key of the ML Feature Table
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  The timestamp for the last time this entity was ingested
  """
  lastIngested: Long

  """
  The display name
  """
  name: String!

  """
  Standardized platform urn where the MLFeatureTable is defined
  """
  platform: DataPlatform!

  """
  MLFeatureTable description
  """
  description: String

  """
  Ownership metadata of the MLFeatureTable
  """
  ownership: Ownership

  """
  Additional read only properties associated the the ML Feature Table
  """
  properties: MLFeatureTableProperties

  """
  Deprecated, use properties field instead
  ModelProperties metadata of the MLFeature
  """
  featureTableProperties: MLFeatureTableProperties @deprecated

  """
  References to internal resources related to the MLFeature
  """
  institutionalMemory: InstitutionalMemory

  """
  Status metadata of the MLFeatureTable
  """
  status: Status

  """
  Deprecation
  """
  deprecation: Deprecation

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult

  """
  The browse paths corresponding to the ML Feature Table. If no Browse Paths have been generated before, this will be null.
  """
  browsePaths: [BrowsePath!]

  """
  The browse path V2 corresponding to an entity. If no Browse Paths V2 have been generated before, this will be null.
  """
  browsePathV2: BrowsePathV2

  """
  Tags applied to entity
  """
  tags: GlobalTags

  """
  The structured glossary terms associated with the entity
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the entity
  """
  domain: DomainAssociation

  """
  The application associated with the entity
  """
  application: ApplicationAssociation

  """
  An additional set of of read write properties
  """
  editableProperties: MLFeatureTableEditableProperties

  """
  Whether or not this entity exists on DataHub
  """
  exists: Boolean

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges
}

type MLFeatureTableEditableProperties {
  """
  The edited description
  """
  description: String
}

type MLFeatureEditableProperties {
  """
  The edited description
  """
  description: String
}

type MLPrimaryKeyEditableProperties {
  """
  The edited description
  """
  description: String
}

type MLModelEditableProperties {
  """
  The edited description
  """
  description: String
}

type MLModelGroupEditableProperties {
  """
  The edited description
  """
  description: String
}

type MLFeatureTableProperties {
  description: String

  mlFeatures: [MLFeature]

  mlPrimaryKeys: [MLPrimaryKey]

  customProperties: [CustomPropertiesEntry!]
}

type HyperParameterMap {
  key: String!
  value: HyperParameterValueType!
}

type StringBox {
  stringValue: String!
}

type IntBox {
  intValue: Int!
}

type FloatBox {
  floatValue: Float!
}

type BooleanBox {
  booleanValue: Boolean!
}

union HyperParameterValueType = StringBox | IntBox | FloatBox | BooleanBox

type MLModelFactorPrompts {
  """
  What are foreseeable salient factors for which MLModel performance may vary, and how were these determined
  """
  relevantFactors: [MLModelFactors!]

  """
  Which factors are being reported, and why were these chosen
  """
  evaluationFactors: [MLModelFactors!]
}

type MLModelFactors {
  """
  Distinct categories with similar characteristics that are present in the evaluation data instances
  """
  groups: [String!]

  """
  Instrumentation used for MLModel
  """
  instrumentation: [String!]

  """
  Environment in which the MLModel is deployed
  """
  environment: [String!]
}

type QuantitativeAnalyses {
  """
  Link to a dashboard with results showing how the model performed with respect to each factor
  """
  unitaryResults: ResultsType

  """
  Link to a dashboard with results showing how the model performed with respect to the intersection of evaluated factors
  """
  intersectionalResults: ResultsType
}

union ResultsType = StringBox

type CaveatsAndRecommendations {
  """
  Caveats on using this MLModel
  """
  caveats: CaveatDetails

  """
  Recommendations on where this MLModel should be used
  """
  recommendations: String

  """
  Ideal characteristics of an evaluation dataset for this MLModel
  """
  idealDatasetCharacteristics: [String!]
}

type CaveatDetails {
  """
  Did the results suggest any further testing
  """
  needsFurtherTesting: Boolean

  """
  Caveat Description
  """
  caveatDescription: String

  """
  Relevant groups that were not represented in the evaluation dataset
  """
  groupsNotRepresented: [String!]
}

type EthicalConsiderations {
  """
  Does the model use any sensitive data eg, protected classes
  """
  data: [String!]

  """
  Is the model intended to inform decisions about matters central to human life or flourishing eg, health or safety
  """
  humanLife: [String!]

  """
  What risk mitigation strategies were used during model development
  """
  mitigations: [String!]

  """
  What risks may be present in model usage
  Try to identify the potential recipients, likelihood, and magnitude of harms
  If these cannot be determined, note that they were considered but remain unknown
  """
  risksAndHarms: [String!]

  """
  Are there any known model use cases that are especially fraught
  This may connect directly to the intended use section
  """
  useCases: [String!]
}

type BaseData {
  """
  Dataset used for the Training or Evaluation of the MLModel
  """
  dataset: String!

  """
  Motivation to pick these datasets
  """
  motivation: String

  """
  Details of Data Proprocessing
  """
  preProcessing: [String!]
}

type Metrics {
  """
  Measures of ML Model performance
  """
  performanceMeasures: [String!]

  """
  Decision Thresholds used if any
  """
  decisionThreshold: [String!]
}

type IntendedUse {
  """
  Primary Use cases for the model
  """
  primaryUses: [String!]

  """
  Primary Intended Users
  """
  primaryUsers: [IntendedUserType!]

  """
  Out of scope uses of the MLModel
  """
  outOfScopeUses: [String!]
}

enum IntendedUserType {
  """
  Developed for Enterprise Users
  """
  ENTERPRISE

  """
  Developed for Hobbyists
  """
  HOBBY

  """
  Developed for Entertainment Purposes
  """
  ENTERTAINMENT
}

type SourceCode {
  """
  Source Code along with types
  """
  sourceCode: [SourceCodeUrl!]
}

type SourceCodeUrl {
  """
  Source Code Url Types
  """
  type: SourceCodeUrlType!

  """
  Source Code Url
  """
  sourceCodeUrl: String!
}

enum SourceCodeUrlType {
  """
  MLModel Source Code
  """
  ML_MODEL_SOURCE_CODE

  """
  Training Pipeline Source Code
  """
  TRAINING_PIPELINE_SOURCE_CODE

  """
  Evaluation Pipeline Source Code
  """
  EVALUATION_PIPELINE_SOURCE_CODE
}

type Cost {
  """
  Type of Cost Code
  """
  costType: CostType!

  """
  Code to which the Cost of this entity should be attributed to ie organizational cost ID
  """
  costValue: CostValue!
}

type CostValue {
  """
  Organizational Cost ID
  """
  costId: Float

  """
  Organizational Cost Code
  """
  costCode: String
}

enum CostType {
  """
  Org Cost Type to which the Cost of this entity should be attributed to
  """
  ORG_COST_TYPE
}

"""
Audit stamp containing a resolved actor
"""
type ResolvedAuditStamp {
  """
  When the audited action took place
  """
  time: Long!

  """
  Who performed the audited action
  """
  actor: CorpUser
}

type SubTypes {
  """
  The sub-types that this entity implements. e.g. Datasets that are views will implement the "view" subtype
  """
  typeNames: [String!]
}

type ApplicationAssociation {
  """
  The application related to the assocaited urn
  """
  application: Application!

  """
  Reference back to the tagged urn for tracking purposes e.g. when sibling nodes are merged together
  """
  associatedUrn: String!
}

type DomainAssociation {
  """
  The domain related to the assocaited urn
  """
  domain: Domain!

  """
  Reference back to the tagged urn for tracking purposes e.g. when sibling nodes are merged together
  """
  associatedUrn: String!
}

"""
A domain, or a logical grouping of Metadata Entities
"""
type Domain implements Entity {
  """
  The primary key of the domain
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Id of the domain
  """
  id: String!

  """
  Properties about a domain
  """
  properties: DomainProperties

  """
  Ownership metadata of the dataset
  """
  ownership: Ownership

  """
  References to internal resources related to the dataset
  """
  institutionalMemory: InstitutionalMemory

  """
  Children entities inside of the Domain
  """
  entities(input: DomainEntitiesInput): SearchResults

  """
  Recursively get the lineage of parent domains for this entity
  """
  parentDomains: ParentDomainsResult

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Display properties for the domain
  """
  displayProperties: DisplayProperties

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges
}

"""
All of the parent domains starting from a single Domain through all of its ancestors
"""
type ParentDomainsResult {
  """
  The number of parent domains bubbling up for this entity
  """
  count: Int!

  """
  A list of parent domains in order from direct parent, to parent's parent etc. If there are no parents, return an empty list
  """
  domains: [Entity!]!
}

"""
Properties about a domain
"""
type DomainProperties {
  """
  Display name of the domain
  """
  name: String!

  """
  Description of the Domain
  """
  description: String
}

"""
Input required to fetch the entities inside of a Domain.
"""
input DomainEntitiesInput {
  """
  Optional query filter for particular entities inside the domain
  """
  query: String

  """
  The offset of the result set
  """
  start: Int

  """
  The number of entities to include in result set
  """
  count: Int

  """
  Optional Facet filters to apply to the result set
  """
  filters: [FacetFilterInput!]
}

"""
Input required to create a new Domain.
"""
input CreateDomainInput {
  """
  Optional! A custom id to use as the primary key identifier for the domain. If not provided, a random UUID will be generated as the id.
  """
  id: String

  """
  Display name for the Domain
  """
  name: String!

  """
  Optional description for the Domain
  """
  description: String

  """
  Optional parent domain urn for the domain
  """
  parentDomain: String
}

"""
Input required when listing DataHub Domains
"""
input ListDomainsInput {
  """
  The starting offset of the result set returned
  """
  start: Int

  """
  The maximum number of Domains to be returned in the result set
  """
  count: Int

  """
  Optional search query
  """
  query: String

  """
  Optional parent domain
  """
  parentDomain: String
}

"""
The result obtained when listing DataHub Domains
"""
type ListDomainsResult {
  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of Domains in the returned result set
  """
  count: Int!

  """
  The total number of Domains in the result set
  """
  total: Int!

  """
  The Domains themselves
  """
  domains: [Domain!]!
}

"""
Input required when getting Business Glossary entities
"""
input GetRootGlossaryEntitiesInput {
  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of Glossary Entities in the returned result set
  """
  count: Int!
}

"""
The result when getting root GlossaryTerms
"""
type GetRootGlossaryTermsResult {
  """
  A list of Glossary Terms without a parent node
  """
  terms: [GlossaryTerm!]!

  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of terms in the returned result
  """
  count: Int!

  """
  The total number of terms in the result set
  """
  total: Int!
}

"""
The result when getting Glossary entities
"""
type GetRootGlossaryNodesResult {
  """
  A list of Glossary Nodes without a parent node
  """
  nodes: [GlossaryNode!]!

  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of nodes in the returned result
  """
  count: Int!

  """
  The total number of nodes in the result set
  """
  total: Int!
}

"""
Input required to create a new Glossary Entity - a Node or a Term.
"""
input CreateGlossaryEntityInput {
  """
  Optional! A custom id to use as the primary key identifier for the domain. If not provided, a random UUID will be generated as the id.
  """
  id: String

  """
  Display name for the Node or Term
  """
  name: String!

  """
  Description for the Node or Term
  """
  description: String

  """
  Optional parent node urn for the Glossary Node or Term
  """
  parentNode: String
}

enum HealthStatus {
  """
  The Asset is in a healthy state
  """
  PASS

  """
  The Asset is in a warning state
  """
  WARN

  """
  The Asset is in a failing (unhealthy) state
  """
  FAIL
}

"""
The type of the health status
"""
enum HealthStatusType {
  """
  Assertions status
  """
  ASSERTIONS

  """
  Incidents status
  """
  INCIDENTS
}

"""
The resolved Health of an Asset
"""
type Health {
  """
  An enum representing the type of health indicator
  """
  type: HealthStatusType!

  """
  The timestamp when the health was reported
  """
  reportedAt: Long

  """
  An enum representing the resolved Health status of an Asset
  """
  status: HealthStatus!

  """
  An optional message describing the resolved health status
  """
  message: String

  """
  If type=INCIDENTS and status=FAIL, populate the details of the latest incident.
  """
  activeIncidentHealthDetails: ActiveIncidentHealthDetails

  """
  If type=ASSERTIONS, populate a breakdown of the assertion statuses by type.
  """
  latestAssertionStatusByType: [AssertionHealthStatusByType!]

  """
  NOTE: @deprecated
  The causes responsible for the health status
  I.e., the assertion urns that are failing
  """
  causes: [String!]
}

type ActiveIncidentHealthDetails {
  """
  The latest incident
  """
  latestIncidentUrn: String

  """
  The title of the latest incident
  """
  latestIncidentTitle: String

  """
  The timestamp when the last incident was updated
  """
  lastActivityAt: Long

  """
  The number of active incidents
  """
  count: Int!
}

type AssertionHealthStatusByType {
  """
  The type group of assertions
  """
  type: AssertionType!

  """
  The status of the assertions in the given type group
  """
  status: HealthStatus!

  """
  The number of assertions in the given type group
  """
  total: Int!

  """
  The number of assertions in the given type group that have the given status (PASS, WARN, FAIL)
  """
  statusCount: Int!

  """
  The timestamp when the last assertion of this type group with the given status ran
  """
  lastStatusResultAt: Long
}

"""
String map entry input
"""
input StringMapEntryInput {
  """
  The key of the map entry
  """
  key: String!

  """
  The value fo the map entry
  """
  value: String
}

"""
Token that allows users to sign up as a native user
"""
type InviteToken {
  """
  The invite token
  """
  inviteToken: String!
}

"""
Input required to generate a password reset token for a native user.
"""
input CreateNativeUserResetTokenInput {
  """
  The urn of the user to reset the password of
  """
  userUrn: String!
}

"""
Token that allows native users to reset their credentials
"""
type ResetToken {
  """
  The reset token
  """
  resetToken: String!
}

"""
Carries information about where an entity originated from.
"""
type Origin {
  """
  Where an entity originated from. Either NATIVE or EXTERNAL
  """
  type: OriginType!

  """
  Only populated if type is EXTERNAL. The externalType of the entity, such as the name of the identity provider.
  """
  externalType: String
}

"""
Enum to define where an entity originated from.
"""
enum OriginType {
  """
  The entity is native to DataHub.
  """
  NATIVE
  """
  The entity is external to DataHub.
  """
  EXTERNAL
  """
  The entity is of unknown origin.
  """
  UNKNOWN
}

"""
Input provided when updating the soft-deleted status for a batch of assets
"""
input BatchUpdateSoftDeletedInput {
  """
  The urns of the assets to soft delete
  """
  urns: [String!]!

  """
  Whether to mark the asset as soft-deleted (hidden)
  """
  deleted: Boolean!
}

"""
Input fields of the chart
"""
type InputFields {
  fields: [InputField]
}

"""
Input field of the chart
"""
type InputField {
  schemaFieldUrn: String
  schemaField: SchemaField
}

"""
An individual setting type for a Corp User.
"""
enum UserSetting {
  """
  Show simplified homepage
  """
  SHOW_SIMPLIFIED_HOMEPAGE

  """
  Show theme v2
  """
  SHOW_THEME_V2
}

"""
Input for updating a user setting
"""
input UpdateUserSettingInput {
  """
  The name of the setting
  """
  name: UserSetting!

  """
  The new value of the setting
  """
  value: Boolean!
}

"""
Input provided when batch assigning a role to a list of users
"""
input BatchAssignRoleInput {
  """
  The urn of the role to assign to the actors. If undefined, will remove the role.
  """
  roleUrn: String

  """
  The urns of the actors to assign the role to
  """
  actors: [String!]!
}

"""
Input provided when listing existing roles
"""
input ListRolesInput {
  """
  The starting offset of the result set returned
  """
  start: Int

  """
  The maximum number of Roles to be returned in the result set
  """
  count: Int

  """
  Optional search query
  """
  query: String
}

"""
The result obtained when listing DataHub Roles
"""
type ListRolesResult {
  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of Roles in the returned result set
  """
  count: Int!

  """
  The total number of Roles in the result set
  """
  total: Int!

  """
  The Roles themselves
  """
  roles: [DataHubRole!]!
}

"""
A DataHub Role is a high-level abstraction on top of Policies that dictates what actions users can take.
"""
type DataHubRole implements Entity {
  """
  The primary key of the role
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  Granular API for querying edges extending from the Role
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  The name of the Role.
  """
  name: String!

  """
  The description of the Role
  """
  description: String!

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]
}

"""
Input provided when getting an invite token
"""
input GetInviteTokenInput {
  """
  The urn of the role to get the invite token for
  """
  roleUrn: String
}

"""
Input provided when creating an invite token
"""
input CreateInviteTokenInput {
  """
  The urn of the role to create the invite token for
  """
  roleUrn: String
}

"""
Input provided when accepting a DataHub role using an invite token
"""
input AcceptRoleInput {
  """
  The token needed to accept the role
  """
  inviteToken: String!
}

"""
The type of post
"""
enum PostType {
  """
  Posts on the home page
  """
  HOME_PAGE_ANNOUNCEMENT

  """
  Posts on an entity page
  """
  ENTITY_ANNOUNCEMENT
}

"""
The type of post
"""
enum PostContentType {
  """
  Text content
  """
  TEXT

  """
  Link content
  """
  LINK
}

"""
The type of media
"""
enum MediaType {
  """
  An image
  """
  IMAGE
}

"""
Input provided when creating a Post
"""
input CreatePostInput {
  """
  The type of post
  """
  postType: PostType!

  """
  The content of the post
  """
  content: UpdatePostContentInput!

  """
  Optional target URN for the post
  """
  resourceUrn: String

  """
  An optional type of a sub resource to attach the Tag to
  """
  subResourceType: SubResourceType

  """
  Optional target subresource for the post
  """
  subResource: String
}

"""
Input provided when creating a Post
"""
input UpdatePostInput {
  """
  The urn of the post to edit or update
  """
  urn: String!

  """
  The type of post
  """
  postType: PostType!

  """
  The content of the post
  """
  content: UpdatePostContentInput!
}

"""
Input provided for filling in a post content
"""
input UpdatePostContentInput {
  """
  The type of post content
  """
  contentType: PostContentType!

  """
  The title of the post
  """
  title: String!

  """
  Optional content of the post
  """
  description: String

  """
  Optional link that the post is associated with
  """
  link: String

  """
  Optional media contained in the post
  """
  media: UpdateMediaInput
}

"""
Input provided for filling in a post content
"""
input UpdateMediaInput {
  """
  The type of media
  """
  type: MediaType!

  """
  The location of the media (a URL)
  """
  location: String!
}

"""
Input provided when listing existing posts
"""
input ListPostsInput {
  """
  The starting offset of the result set returned
  """
  start: Int

  """
  The maximum number of Roles to be returned in the result set
  """
  count: Int

  """
  Optional search query
  """
  query: String

  """
  Optional resource urn
  """
  resourceUrn: String

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]
}

"""
The result obtained when listing Posts
"""
type ListPostsResult {
  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of Roles in the returned result set
  """
  count: Int!

  """
  The total number of Roles in the result set
  """
  total: Int!

  """
  The Posts themselves
  """
  posts: [Post!]!
}

"""
Input provided when creating a Post
"""
type Post implements Entity {
  """
  The primary key of the Post
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  Granular API for querying edges extending from the Post
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  The type of post
  """
  postType: PostType!

  """
  The content of the post
  """
  content: PostContent!

  """
  When the post was last modified
  """
  lastModified: AuditStamp!
}

"""
Post content
"""
type PostContent {
  """
  The type of post content
  """
  contentType: PostContentType!

  """
  The title of the post
  """
  title: String!

  """
  Optional content of the post
  """
  description: String

  """
  Optional link that the post is associated with
  """
  link: String

  """
  Optional media contained in the post
  """
  media: Media
}

"""
Media content
"""
type Media {
  """
  The type of media
  """
  type: MediaType!

  """
  The location of the media (a URL)
  """
  location: String!
}

"""
The type of a DataHub View
"""
enum DataHubViewType {
  """
  A personal view - e.g. saved filters
  """
  PERSONAL

  """
  A global view, e.g. role view
  """
  GLOBAL
}

"""
An DataHub View - Filters that are applied across the application automatically.
"""
type DataHubView implements Entity {
  """
  The primary key of the View
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  The type of the View
  """
  viewType: DataHubViewType!

  """
  The name of the View
  """
  name: String!

  """
  The description of the View
  """
  description: String

  """
  The definition of the View
  """
  definition: DataHubViewDefinition!

  """
  Granular API for querying edges extending from the View
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult
}

"""
An DataHub View Definition
"""
type DataHubViewDefinition {
  """
  A set of filters to apply. If left empty, then ALL entity types are in scope.
  """
  entityTypes: [EntityType!]!

  """
  A set of filters to apply. If left empty, then no filters will be applied.
  """
  filter: DataHubViewFilter!
}

"""
A Logical Operator, AND or OR.
"""
enum LogicalOperator {
  """
  An AND operator.
  """
  AND

  """
  An OR operator.
  """
  OR
}

"""
A DataHub View Filter. Note that
"""
type DataHubViewFilter {
  """
  The operator used to combine the filters.
  """
  operator: LogicalOperator!

  """
  A set of filters combined using the operator. If left empty, then no filters will be applied.
  """
  filters: [FacetFilter!]!
}

"""
Input provided when listing DataHub Views
"""
input ListMyViewsInput {
  """
  The starting offset of the result set returned
  """
  start: Int

  """
  The maximum number of Views to be returned in the result set
  """
  count: Int

  """
  Optional search query
  """
  query: String

  """
  Optional - List the type of View to filter for.
  """
  viewType: DataHubViewType
}

"""
Input provided when listing DataHub Global Views
"""
input ListGlobalViewsInput {
  """
  The starting offset of the result set returned
  """
  start: Int

  """
  The maximum number of Views to be returned in the result set
  """
  count: Int

  """
  Optional search query
  """
  query: String
}

"""
The result obtained when listing DataHub Views
"""
type ListViewsResult {
  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of Views in the returned result set
  """
  count: Int!

  """
  The total number of Views in the result set
  """
  total: Int!

  """
  The Views themselves
  """
  views: [DataHubView!]!
}

"""
Input provided when creating a DataHub View
"""
input CreateViewInput {
  """
  The type of View
  """
  viewType: DataHubViewType!

  """
  The name of the View
  """
  name: String!

  """
  An optional description of the View
  """
  description: String

  """
  The view definition itself
  """
  definition: DataHubViewDefinitionInput!
}

"""
Input required for creating a DataHub View Definition
"""
input DataHubViewDefinitionInput {
  """
  A set of entity types that the view applies for. If left empty, then ALL entities will be in scope.
  """
  entityTypes: [EntityType!]!

  """
  A set of filters to apply.
  """
  filter: DataHubViewFilterInput!
}

"""
Input required for creating a DataHub View Definition
"""
input DataHubViewFilterInput {
  """
  The operator used to combine the filters.
  """
  operator: LogicalOperator!

  """
  A set of filters combined via an operator. If left empty, then no filters will be applied.
  """
  filters: [FacetFilterInput!]!
}

"""
Input provided when updating a DataHub View
"""
input UpdateViewInput {
  """
  The name of the View
  """
  name: String

  """
  An optional description of the View
  """
  description: String

  """
  The view definition itself
  """
  definition: DataHubViewDefinitionInput
}

"""
Input required to update a users settings.
"""
input UpdateCorpUserViewsSettingsInput {
  """
  The URN of the View that serves as this user's personal default.
  If not provided, any existing default view will be removed.
  """
  defaultView: String
}

"""
Input required to update a user's home page settings.
"""
input UpdateUserHomePageSettingsInput {
  """
  The URN of the page template to be rendered on the home page for the user.
  """
  pageTemplate: String

  """
  The list of urns of announcement posts dismissed by the user.
  """
  newDismissedAnnouncements: [String]
}

"""
Information required to render an embedded version of an asset
"""
type Embed {
  """
  A URL which can be rendered inside of an iframe.
  """
  renderUrl: String
}

"""
Input required to set or clear information related to rendering a Data Asset inside of DataHub.
"""
input UpdateEmbedInput {
  """
  The URN associated with the Data Asset to update. Only dataset, dashboard, and chart urns are currently supported.
  """
  urn: String!

  """
  Set or clear a URL used to render an embedded asset.
  """
  renderUrl: String
}

"""
A query language / dialect.
"""
enum QueryLanguage {
  """
  Standard ANSI SQL
  """
  SQL
}

"""
The source of the query
"""
enum QuerySource {
  """
  The query was provided manually, e.g. from the UI.
  """
  MANUAL

  """
  The query was extracted by the system, e.g. from a dashboard.
  """
  SYSTEM
}

"""
An individual Query Statement
"""
type QueryStatement {
  """
  The query statement value
  """
  value: String!

  """
  The language for the Query Statement
  """
  language: QueryLanguage!
}

"""
Properties about an individual Query
"""
type QueryProperties {
  """
  The Query statement itself
  """
  statement: QueryStatement!

  """
  The source of the Query
  """
  source: QuerySource!

  """
  The name of the Query
  """
  name: String

  """
  The description of the Query
  """
  description: String

  """
  An Audit Stamp corresponding to the creation of this resource
  """
  created: AuditStamp!

  """
  An Audit Stamp corresponding to the update of this resource
  """
  lastModified: AuditStamp!

  """
  The asset that this query originated from, e.g. a View, a dbt Model, etc.
  """
  origin: Entity

  """
  Custom properties of the Data Product
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
The subject for a Query
"""
type QuerySubject {
  """
  The dataset which is the subject of the Query
  """
  dataset: Dataset!

  """
  The schema field which is the subject of the Query.
  This will be populated if the subject is specifically a schema field.
  """
  schemaField: SchemaFieldEntity
}

"""
An individual Query
"""
type QueryEntity implements Entity {
  """
  A primary key associated with the Query
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Properties about the Query
  """
  properties: QueryProperties

  """
  Subjects for the query
  """
  subjects: [QuerySubject!]

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Platform from which the Query was detected
  """
  platform: DataPlatform
}

"""
Input required for creating a Query Statement
"""
input QueryStatementInput {
  """
  The query text
  """
  value: String!

  """
  The query language
  """
  language: QueryLanguage!
}

"""
Input properties required for creating a Query
"""
input CreateQueryPropertiesInput {
  """
  An optional display name for the Query
  """
  name: String

  """
  An optional description for the Query
  """
  description: String

  """
  The Query contents
  """
  statement: QueryStatementInput!
}

"""
Input required for creating a Query. For now, only datasets are supported.
"""
input CreateQuerySubjectInput {
  """
  The urn of the dataset that is the subject of the query
  """
  datasetUrn: String!
}

"""
Input required for creating a Query. Requires the 'Edit Queries' privilege for all query subjects.
"""
input CreateQueryInput {
  """
  Properties about the Query
  """
  properties: CreateQueryPropertiesInput!

  """
  Subjects for the query
  """
  subjects: [CreateQuerySubjectInput!]!
}

"""
Input properties required for creating a Query. Any non-null fields will be updated if provided.
"""
input UpdateQueryPropertiesInput {
  """
  An optional display name for the Query
  """
  name: String

  """
  An optional description for the Query
  """
  description: String

  """
  The Query contents
  """
  statement: QueryStatementInput
}

"""
Input required for creating a Query. For now, only datasets are supported.
"""
input UpdateQuerySubjectInput {
  """
  The urn of the dataset that is the subject of the query
  """
  datasetUrn: String!
}

"""
Input required for updating an existing Query. Requires the 'Edit Queries' privilege for all query subjects.
"""
input UpdateQueryInput {
  """
  Properties about the Query
  """
  properties: UpdateQueryPropertiesInput

  """
  Subjects for the query
  """
  subjects: [UpdateQuerySubjectInput!]
}

"""
Input required for listing query entities
"""
input ListQueriesInput {
  """
  The starting offset of the result set returned
  """
  start: Int

  """
  The maximum number of Queries to be returned in the result set
  """
  count: Int

  """
  A raw search query
  """
  query: String

  """
  An optional source for the query
  """
  source: QuerySource

  """
  An optional Urn for the parent dataset that the query is associated with.
  """
  datasetUrn: String

  """
  Optional - Information on how to sort the list queries result
  """
  sortInput: SortQueriesInput

  """
  A list of disjunctive criterion for the filter. (or operation to combine filters)
  """
  orFilters: [AndFilterInput!]
}

"""
Results when listing entity queries
"""
type ListQueriesResult {
  """
  The starting offset of the result set
  """
  start: Int!

  """
  The number of results to be returned
  """
  count: Int!

  """
  The total number of results in the result set
  """
  total: Int!

  """
  The Queries themselves
  """
  queries: [QueryEntity!]!
}

"""
A Data Product, or a logical grouping of Metadata Entities
"""
type DataProduct implements Entity {
  """
  The primary key of the Data Product
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Properties about a Data Product
  """
  properties: DataProductProperties

  """
  Ownership metadata of the Data Product
  """
  ownership: Ownership

  """
  References to internal resources related to the Data Product
  """
  institutionalMemory: InstitutionalMemory

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Children entities inside of the DataProduct
  """
  entities(input: SearchAcrossEntitiesInput): SearchResults

  """
  The structured glossary terms associated with the Data Product
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the Data Product
  """
  domain: DomainAssociation

  """
  The application associated with the data product
  """
  application: ApplicationAssociation

  """
  Tags used for searching Data Product
  """
  tags: GlobalTags

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Dataset
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges
}

"""
Properties about a domain
"""
type DataProductProperties {
  """
  Display name of the Data Product
  """
  name: String!

  """
  Description of the Data Product
  """
  description: String

  """
  External URL for the DataProduct (most likely GitHub repo where Data Products are managed as code)
  """
  externalUrl: String

  """
  Number of children entities inside of the Data Product. This number includes soft deleted entities.
  """
  numAssets: Int

  """
  Custom properties of the Data Product
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
Input required to fetch the entities inside of a Data Product.
"""
input DataProductEntitiesInput {
  """
  Optional query filter for particular entities inside the Data Product
  """
  query: String

  """
  The offset of the result set
  """
  start: Int

  """
  The number of entities to include in result set
  """
  count: Int

  """
  Optional Facet filters to apply to the result set
  """
  filters: [FacetFilterInput!]
}

"""
Input required for creating a DataProduct.
"""
input CreateDataProductInput {
  """
  Properties about the Query
  """
  properties: CreateDataProductPropertiesInput!

  """
  The primary key of the Domain
  """
  domainUrn: String!
  """
  An optional id for the new data product
  """
  id: String
}

"""
Input properties required for creating a DataProduct
"""
input CreateDataProductPropertiesInput {
  """
  A display name for the DataProduct
  """
  name: String!

  """
  An optional description for the DataProduct
  """
  description: String
}

"""
Input properties required for update a DataProduct
"""
input UpdateDataProductInput {
  """
  A display name for the DataProduct
  """
  name: String

  """
  An optional description for the DataProduct
  """
  description: String
}

"""
Input properties required for batch setting a DataProduct on other entities
"""
input BatchSetDataProductInput {
  """
  The urn of the data product you are setting on a group of resources.
  If this is null, the Data Product will be unset for the given resources.
  """
  dataProductUrn: String

  """
  The urns of the entities the given data product should be set on
  """
  resourceUrns: [String!]!
}

"""
An Application, or a grouping of Entities for a single business purpose. Compared with Data Products, Applications represent a grouping of tables that exist to serve a specific
purpose. However, unlike Data Products, they don't represent groups that are tailored to be consumed for any particular purpose. Often, the assets in Applications power specific
outcomes, for example a Pricing Application.
"""
type Application implements Entity {
  """
  The primary key of the Application
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Properties about an Application
  """
  properties: ApplicationProperties

  """
  Ownership metadata of the Application
  """
  ownership: Ownership

  """
  References to internal resources related to the Application
  """
  institutionalMemory: InstitutionalMemory

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  The structured glossary terms associated with the Application
  """
  glossaryTerms: GlossaryTerms

  """
  The Domain associated with the Application
  """
  domain: DomainAssociation

  """
  Tags used for searching Application
  """
  tags: GlobalTags

  """
  Experimental API.
  For fetching extra entities that do not have custom UI code yet
  """
  aspects(input: AspectParams): [RawAspect!]

  """
  Structured properties about this asset
  """
  structuredProperties: StructuredProperties

  """
  The forms associated with the Application
  """
  forms: Forms

  """
  Privileges given to a user relevant to this entity
  """
  privileges: EntityPrivileges
}

"""
Properties about an Application
"""
type ApplicationProperties {
  """
  Display name of the Application
  """
  name: String!

  """
  Description of the Application
  """
  description: String

  """
  External URL for the Appliation (most likely GitHub repo where Application may be managed as code)
  """
  externalUrl: String

  """
  Number of children entities inside of the Application. This number includes soft deleted entities.
  """
  numAssets: Int

  """
  Custom properties of the Application
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
Input required to fetch the entities inside of a Application.
"""
input ApplicationEntitiesInput {
  """
  Optional query filter for particular entities inside the Application
  """
  query: String

  """
  The offset of the result set
  """
  start: Int

  """
  The number of entities to include in result set
  """
  count: Int

  """
  Optional Facet filters to apply to the result set
  """
  filters: [FacetFilterInput!]
}

"""
Input required for creating a Application.
"""
input CreateApplicationInput {
  """
  Properties about the Application
  """
  properties: CreateApplicationPropertiesInput!

  """
  An Optional Domain
  """
  domainUrn: String

  """
  An optional id for the new application
  """
  id: String
}

"""
Input properties required for creating a Application
"""
input CreateApplicationPropertiesInput {
  """
  A display name for the Application
  """
  name: String!

  """
  An optional description for the Application
  """
  description: String
}

"""
Input properties required for update a Application
"""
input UpdateApplicationInput {
  """
  A display name for the Application
  """
  name: String

  """
  An optional description for the Application
  """
  description: String
}

"""
Input properties required for batch setting a Application on other entities
"""
input BatchSetApplicationInput {
  """
  The urn of the application you are setting on a group of resources.
  If this is null, the Application will be unset for the given resources.
  """
  applicationUrn: String

  """
  The urns of the entities the given application should be set on
  """
  resourceUrns: [String!]!
}

"""
Properties about an individual Custom Ownership Type.
"""
type OwnershipTypeInfo {
  """
  The name of the Custom Ownership Type
  """
  name: String!

  """
  The description of the Custom Ownership Type
  """
  description: String

  """
  An Audit Stamp corresponding to the creation of this resource
  """
  created: AuditStamp

  """
  An Audit Stamp corresponding to the update of this resource
  """
  lastModified: AuditStamp
}

"""
A single Custom Ownership Type
"""
type OwnershipTypeEntity implements Entity {
  """
  A primary key associated with the custom ownership type.
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Information about the Custom Ownership Type
  """
  info: OwnershipTypeInfo

  """
  Status of the Custom Ownership Type
  """
  status: Status

  """
  Granular API for querying edges extending from the Custom Ownership Type
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult
}

"""
Input required for listing custom ownership types entities
"""
input ListOwnershipTypesInput {
  """
  The starting offset of the result set returned, default is 0
  """
  start: Int

  """
  The maximum number of Custom Ownership Types to be returned in the result set, default is 20
  """
  count: Int

  """
  Optional search query
  """
  query: String

  """
  Optional Facet filters to apply to the result set
  """
  filters: [FacetFilterInput!]
}

"""
Results when listing custom ownership types.
"""
type ListOwnershipTypesResult {
  """
  The starting offset of the result set
  """
  start: Int!

  """
  The number of results to be returned
  """
  count: Int!

  """
  The total number of results in the result set
  """
  total: Int!

  """
  The Custom Ownership Types themselves
  """
  ownershipTypes: [OwnershipTypeEntity!]!
}

input CreateOwnershipTypeInput {
  """
  The name of the Custom Ownership Type
  """
  name: String!

  """
  The description of the Custom Ownership Type
  """
  description: String!
}

input UpdateOwnershipTypeInput {
  """
  The name of the Custom Ownership Type
  """
  name: String

  """
  The description of the Custom Ownership Type
  """
  description: String
}

"""
A standardized type of a user
"""
type DataHubPersona {
  """
  The urn of the persona type
  """
  urn: String!
}

"""
Describes a generic filter on a dataset
"""
type DatasetFilter {
  """
  Type of partition
  """
  type: DatasetFilterType!

  """
  The raw query if using a SQL FilterType
  """
  sql: String
}

"""
Type of partition
"""
enum DatasetFilterType {
  """
  Use a SQL string to apply the filter
  """
  SQL
}

"""
Input required to create or update a DatasetFilter
"""
input DatasetFilterInput {
  """
  Type of partition
  """
  type: DatasetFilterType!

  """
  The raw query if using a SQL FilterType
  """
  sql: String
}

"""
An entity type registered in DataHub
"""
type EntityTypeEntity implements Entity {
  """
  A primary key associated with the Query
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Info about this type including its name
  """
  info: EntityTypeInfo!

  """
  Granular API for querying edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult
}

"""
Properties about an individual entity type
"""
type EntityTypeInfo {
  """
  The standard entity type
  """
  type: EntityType!

  """
  The fully qualified name of the entity type. This includes its namespace
  """
  qualifiedName: String!

  """
  The display name of this type
  """
  displayName: String

  """
  The description of this type
  """
  description: String
}

"""
A restricted entity that the user does not have full permissions to view.
This entity type does not relate to an entity type in the database.
"""
type Restricted implements Entity & EntityWithRelationships {
  """
  The primary key of the restricted entity
  """
  urn: String!

  """
  The standard Entity Type
  """
  type: EntityType!

  """
  Edges extending from this entity
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult

  """
  Edges extending from this entity grouped by direction in the lineage graph
  """
  lineage(input: LineageInput!): EntityLineageResult
}

"""
A Business Attribute, or a logical schema Field
"""
type BusinessAttribute implements Entity {
  """
  The primary key of the Data Product
  """
  urn: String!

  """
  A standard Entity Type
  """
  type: EntityType!

  """
  Properties about a Business Attribute
  """
  properties: BusinessAttributeInfo

  """
  Ownership metadata of the Business Attribute
  """
  ownership: Ownership

  """
  References to internal resources related to Business Attribute
  """
  institutionalMemory: InstitutionalMemory

  """
  Status of the Dataset
  """
  status: Status

  """
  List of relationships between the source Entity and some destination entities with a given types
  """
  relationships(input: RelationshipsInput!): EntityRelationshipsResult
}

"""
Business Attribute type
"""
type BusinessAttributeInfo {
  """
  name of the business attribute
  """
  name: String!

  """
  description of business attribute
  """
  description: String

  """
  Tags associated with the business attribute
  """
  tags: GlobalTags

  """
  Glossary terms associated with the business attribute
  """
  glossaryTerms: GlossaryTerms

  """
  Platform independent field type of the field
  """
  type: SchemaFieldDataType

  """
  A list of platform specific metadata tuples
  """
  customProperties: [CustomPropertiesEntry!]

  """
  An AuditStamp corresponding to the creation of this chart
  """
  created: AuditStamp!

  """
  An AuditStamp corresponding to the modification of this chart
  """
  lastModified: AuditStamp!

  """
  An optional AuditStamp corresponding to the deletion of this chart
  """
  deleted: AuditStamp
}

"""
Input required for creating a BusinessAttribute.
"""
input CreateBusinessAttributeInput {
  """
  Optional! A custom id to use as the primary key identifier. If not provided, a random UUID will be generated as the id.
  """
  id: String

  """
  name of the business attribute
  """
  name: String!

  """
  description of business attribute
  """
  description: String

  """
  Platform independent field type of the field
  """
  type: SchemaFieldDataType
}

input BusinessAttributeInfoInput {
  """
  name of the business attribute
  """
  name: String!

  """
  description of business attribute
  """
  description: String

  """
  Platform independent field type of the field
  """
  type: SchemaFieldDataType
}

"""
Input required to update Business Attribute
"""
input UpdateBusinessAttributeInput {
  """
  name of the business attribute
  """
  name: String

  """
  business attribute description
  """
  description: String

  """
  type
  """
  type: SchemaFieldDataType
}

"""
Input required to attach Business Attribute
If businessAttributeUrn is null, then it will remove the business attribute from the resource
"""
input AddBusinessAttributeInput {
  """
  The urn of the business attribute to add
  """
  businessAttributeUrn: String!

  """
  resource urns to add the business attribute to
  """
  resourceUrn: [ResourceRefInput!]!
}

"""
Business attributes attached to the metadata
"""
type BusinessAttributes {
  """
  Business Attribute attached to the Metadata Entity
  """
  businessAttribute: BusinessAttributeAssociation
}

"""
Input required to attach business attribute to an entity
"""
type BusinessAttributeAssociation {
  """
  Business Attribute itself
  """
  businessAttribute: BusinessAttribute!

  """
  Reference back to the associated urn for tracking purposes e.g. when sibling nodes are merged together
  """
  associatedUrn: String!
}

"""
Input provided when listing Business Attribute
"""
input ListBusinessAttributesInput {
  """
  The starting offset of the result set returned
  """
  start: Int

  """
  The maximum number of Business Attributes to be returned in the result set
  """
  count: Int

  """
  Optional search query
  """
  query: String
}

"""
The result obtained when listing Business Attribute
"""
type ListBusinessAttributesResult {
  """
  The starting offset of the result set returned
  """
  start: Int!

  """
  The number of Business Attributes in the returned result set
  """
  count: Int!

  """
  The total number of Business Attributes in the result set
  """
  total: Int!

  """
  The Business Attributes
  """
  businessAttributes: [BusinessAttribute!]!
}

"""
A cron schedule
"""
type CronSchedule {
  """
  A cron-formatted execution interval, as a cron string, e.g. 1 * * * *
  """
  cron: String!

  """
  Timezone in which the cron interval applies, e.g. America/Los_Angeles
  """
  timezone: String!
}

"""
Properties describing a data process instance's execution metadata
"""
type DataProcessInstanceProperties {
  """
  The display name of this process instance
  """
  name: String!

  """
  URL to view this process instance in the external system
  """
  externalUrl: String

  """
  When this process instance was created
  """
  created: AuditStamp!

  """
  Additional custom properties specific to this process instance
  """
  customProperties: [CustomPropertiesEntry!]
}

"""
Properties specific to an ML model training run instance
"""
type MLTrainingRunProperties {
  """
  Unique identifier for this training run
  """
  id: String

  """
  List of URLs to access training run outputs (e.g. model artifacts, logs)
  """
  outputUrls: [String]

  """
  Hyperparameters used in this training run
  """
  hyperParams: [MLHyperParam]

  """
  Performance metrics recorded during this training run
  """
  trainingMetrics: [MLMetric]
}

extend type DataProcessInstance {
  """
  Additional read only properties associated with the Data Process Instance
  """
  properties: DataProcessInstanceProperties

  """
  The specific instance of the data platform that this entity belongs to
  """
  dataPlatformInstance: DataPlatformInstance

  """
  Sub Types that this entity implements
  """
  subTypes: SubTypes

  """
  The parent container in which the entity resides
  """
  container: Container

  """
  Standardized platform urn where the data process instance is defined
  """
  platform: DataPlatform

  """
  Recursively get the lineage of containers for this entity
  """
  parentContainers: ParentContainersResult

  """
  Additional properties when subtype is Training Run
  """
  mlTrainingRunProperties: MLTrainingRunProperties

  """
  The parent entity whose run instance it is
  """
  parentTemplate: Entity
}

"""
Details about how a policy was evaluated for a given actor and resource
"""
type PolicyEvaluationDetail {
  """
  The policy that was evaluated
  """
  policyName: String!

  """
  The reason for deny for this policy
  """
  reason: String!
}
