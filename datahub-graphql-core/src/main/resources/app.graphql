# DataHub UI Application-Specific GraphQL Types

extend type Query {
  """
  Fetch details associated with the authenticated user, provided via an auth cookie or header
  """
  me: AuthenticatedUser

  """
  Fetch configurations
  Used by DataHub UI
  """
  appConfig: AppConfig

  """
  Fetch the Global Settings related to the Views feature.
  Requires the 'Manage Global Views' Platform Privilege.
  """
  globalViewsSettings: GlobalViewsSettings

  """
  Fetch the global settings related to the docs propagation feature.
  """
  docPropagationSettings: DocPropagationSettings

  """
  Global settings related to the home page for an instance
  """
  globalHomePageSettings: GlobalHomePageSettings
}

extend type Mutation {
  """
  Update the global settings related to the Views feature.
  Requires the 'Manage Global Views' Platform Privilege.
  """
  updateGlobalViewsSettings(input: UpdateGlobalViewsSettingsInput!): Boolean!

  """
  Update the doc propagation settings.
  """
  updateDocPropagationSettings(
    input: UpdateDocPropagationSettingsInput!
  ): Boolean!

  """
  Update the applications settings.
  """
  updateApplicationsSettings(input: UpdateApplicationsSettingsInput!): Boolean!
}

"""
Information about the currently authenticated user
"""
type AuthenticatedUser {
  """
  The user information associated with the authenticated user, including properties used in rendering the profile
  """
  corpUser: CorpUser!

  """
  The privileges assigned to the currently authenticated user, which dictates which parts of the UI they should be able to use
  """
  platformPrivileges: PlatformPrivileges!
}

"""
The platform privileges that the currently authenticated user has
"""
type PlatformPrivileges {
  """
  Whether the user should be able to view analytics
  """
  viewAnalytics: Boolean!

  """
  Whether the user should be able to manage policies
  """
  managePolicies: Boolean!

  """
  Whether the user should be able to manage users & groups
  """
  manageIdentities: Boolean!

  """
  Whether the user should be able to generate personal access tokens
  """
  generatePersonalAccessTokens: Boolean!

  """
  Whether the user should be able to create new Domains
  """
  createDomains: Boolean!

  """
  Whether the user should be able to manage Domains
  """
  manageDomains: Boolean!

  """
  Whether the user is able to manage UI-based ingestion
  """
  manageIngestion: Boolean!

  """
  Whether the user is able to manage UI-based secrets
  """
  manageSecrets: Boolean!

  """
  Whether the user should be able to manage tokens on behalf of other users.
  """
  manageTokens: Boolean!

  """
  Whether the user is able to view Tests
  """
  viewTests: Boolean!

  """
  Whether the user is able to manage Tests
  """
  manageTests: Boolean!

  """
  Whether the user should be able to manage Glossaries
  """
  manageGlossaries: Boolean!

  """
  Whether the user is able to manage user credentials
  """
  manageUserCredentials: Boolean!

  """
  Whether the user should be able to create new Tags
  """
  createTags: Boolean!

  """
  Whether the user should be able to create and delete all Tags
  """
  manageTags: Boolean!

  """
  Whether the user should be able to view the tags management page.
  """
  viewManageTags: Boolean!

  """
  Whether the user should be able to create, update, and delete global views.
  """
  manageGlobalViews: Boolean!

  """
  Whether the user should be able to create, update, and delete ownership types.
  """
  manageOwnershipTypes: Boolean!

  """
  Whether the user can create and delete posts pinned to the home page.
  """
  manageGlobalAnnouncements: Boolean!

  """
  Whether the user can create Business Attributes.
  """
  createBusinessAttributes: Boolean!

  """
  Whether the user can manage Business Attributes.
  """
  manageBusinessAttributes: Boolean!

  """
  Whether the user can create, edit, and delete structured properties.
  """
  manageStructuredProperties: Boolean!

  """
  Whether the user can view the manage structured properties page.
  """
  viewStructuredPropertiesPage: Boolean!

  """
  Whether the user can manage applications.
  """
  manageApplications: Boolean!

  """
  Whether the user can manage platform features.
  """
  manageFeatures: Boolean!
}

"""
Variants of APIs used in the Search bar to get data
"""
enum SearchBarAPI {
  AUTOCOMPLETE_FOR_MULTIPLE
  SEARCH_ACROSS_ENTITIES
}

"""
Configurations related to the Search bar
"""
type SearchBarConfig {
  """
  API variant
  """
  apiVariant: SearchBarAPI!
}

"""
Variants of APIs used in the Search bar to get data
"""
enum PersonalSidebarSection {
  """
  The section containing groups you are in
  """
  YOUR_GROUPS
  """
  The section containing assets you own
  """
  YOUR_ASSETS
  """
  The section containing domains you own
  """
  YOUR_DOMAINS
  """
  The section containing glossary nodes you own
  """
  YOUR_GLOSSARY_NODES
  """
  The section containing tags you own
  """
  YOUR_TAGS
}

"""
Configurations related to the Search bar
"""
type HomePageConfig {
  """
  The section that comes first on the personal sidebar on the homepage
  """
  firstInPersonalSidebar: PersonalSidebarSection!
}

"""
Config loaded at application boot time
This configuration dictates the behavior of the UI, such as which features are enabled or disabled
"""
type AppConfig {
  """
  App version
  """
  appVersion: String

  """
  Auth-related configurations
  """
  authConfig: AuthConfig!

  """
  Configurations related to the Analytics Feature
  """
  analyticsConfig: AnalyticsConfig!

  """
  Configurations related to the Policies Feature
  """
  policiesConfig: PoliciesConfig!

  """
  Configurations related to the User & Group management
  """
  identityManagementConfig: IdentityManagementConfig!

  """
  Configurations related to UI-based ingestion
  """
  managedIngestionConfig: ManagedIngestionConfig!

  """
  Configurations related to Lineage
  """
  lineageConfig: LineageConfig!

  """
  Configurations related to visual appearance, allows styling the UI without rebuilding the bundle
  """
  visualConfig: VisualConfig!

  """
  Configurations related to tracking users in the app
  """
  telemetryConfig: TelemetryConfig!

  """
  Configurations related to DataHub tests
  """
  testsConfig: TestsConfig!

  """
  Configurations related to DataHub Views
  """
  viewsConfig: ViewsConfig!

  """
  Configurations related to the Search bar
  """
  searchBarConfig: SearchBarConfig!

  """
  Feature flags telling the UI whether a feature is enabled or not
  """
  featureFlags: FeatureFlagsConfig!

  """
  Configuration related to the DataHub Chrome Extension
  """
  chromeExtensionConfig: ChromeExtensionConfig!

  """
  Configuration related to the home page
  """
  homePageConfig: HomePageConfig!
}

"""
Configurations related to visual appearance of the app
"""
type VisualConfig {
  """
  Custom logo url for the homepage & top banner
  """
  logoUrl: String

  """
  Custom favicon url for the homepage & top banner
  """
  faviconUrl: String

  """
  Custom app title to show in the browser tab
  """
  appTitle: String

  """
  Boolean flag disabling viewing the Business Glossary page for users without the 'Manage Glossaries' privilege
  """
  hideGlossary: Boolean

  """
  Configuration for the queries tab
  """
  queriesTab: QueriesTabConfig

  """
  Configuration for the queries tab
  """
  entityProfiles: EntityProfilesConfig

  """
  Configuration for search results
  """
  searchResult: SearchResultsVisualConfig

  """
  Show full title in lineage view by default
  """
  showFullTitleInLineage: Boolean

  """
  Configuration for custom theme-ing
  """
  theme: ThemeConfig

  """
  Configuration for the application sidebar section
  """
  application: ApplicationConfig
}

"""
Configuration for the application sidebar section
"""
type ApplicationConfig {
  """
  Whether to show the application sidebar section even when empty
  """
  showSidebarSectionWhenEmpty: Boolean

  """
  Whether to show the application in the navigation sidebar
  """
  showApplicationInNavigation: Boolean
}

"""
Configuration for the queries tab
"""
type QueriesTabConfig {
  """
  Number of queries to show in the queries tab
  """
  queriesTabResultSize: Int
}

"""
Configuration for different entity profiles
"""
type EntityProfilesConfig {
  """
  The configurations for a Domain entity profile
  """
  domain: EntityProfileConfig
}

"""
Configuration for an entity profile
"""
type EntityProfileConfig {
  """
  The enum value from EntityProfileTab for which tab should be showed by default on
  entity profile pages. If null, rely on default sorting from React code.
  """
  defaultTab: String
}

"""
Configuration for a search result
"""
type SearchResultsVisualConfig {
  """
  Whether a search result should highlight the name/description if it was matched on those fields.
  """
  enableNameHighlight: Boolean
}

"""
Configurations related to tracking users in the app
"""
type TelemetryConfig {
  """
  Env variable for whether or not third party logging should be enabled for this instance
  """
  enableThirdPartyLogging: Boolean
}

"""
Configurations related to Lineage
"""
type LineageConfig {
  """
  Whether the backend support impact analysis feature
  """
  supportsImpactAnalysis: Boolean!
}

"""
Configurations related to the Analytics Feature
"""
type AnalyticsConfig {
  """
  Whether the Analytics feature is enabled and should be displayed
  """
  enabled: Boolean!
}

"""
Configurations related to auth
"""
type AuthConfig {
  """
  Whether token-based auth is enabled.
  """
  tokenAuthEnabled: Boolean!
}

"""
Configurations related to the Policies Feature
"""
type PoliciesConfig {
  """
  Whether the policies feature is enabled and should be displayed in the UI
  """
  enabled: Boolean!

  """
  A list of platform privileges to display in the Policy Builder experience
  """
  platformPrivileges: [Privilege!]!

  """
  A list of resource privileges to display in the Policy Builder experience
  """
  resourcePrivileges: [ResourcePrivileges!]!
}

"""
An individual DataHub Access Privilege
"""
type Privilege {
  """
  Standardized privilege type, serving as a unique identifier for a privilege eg EDIT_ENTITY
  """
  type: String!

  """
  The name to appear when displaying the privilege, eg Edit Entity
  """
  displayName: String

  """
  A description of the privilege to display
  """
  description: String
}

"""
A privilege associated with a particular resource type
A resource is most commonly a DataHub Metadata Entity
"""
type ResourcePrivileges {
  """
  Resource type associated with the Access Privilege, eg dataset
  """
  resourceType: String!

  """
  The name to used for displaying the resourceType
  """
  resourceTypeDisplayName: String

  """
  An optional entity type to use when performing search and navigation to the entity
  """
  entityType: EntityType

  """
  A list of privileges that are supported against this resource
  """
  privileges: [Privilege!]!
}

"""
Configurations related to Identity Management
"""
type IdentityManagementConfig {
  """
  Whether identity management screen is able to be shown in the UI
  """
  enabled: Boolean!
}

"""
Configurations related to managed, UI based ingestion
"""
type ManagedIngestionConfig {
  """
  Whether ingestion screen is enabled in the UI
  """
  enabled: Boolean!
}

"""
Configurations related to DataHub Tests feature
"""
type TestsConfig {
  """
  Whether Tests feature is enabled
  """
  enabled: Boolean!
}

"""
Configurations related to DataHub Views feature
"""
type ViewsConfig {
  """
  Whether Views feature is enabled
  """
  enabled: Boolean!
}

"""
Configurations related to DataHub Views feature
"""
type FeatureFlagsConfig {
  """
  Whether read only mode is enabled on an instance.
  Right now this only affects ability to edit user profile image URL but can be extended.
  """
  readOnlyModeEnabled: Boolean!

  """
  Whether search filters V2 should be shown or the default filter side-panel
  """
  showSearchFiltersV2: Boolean!

  """
  Whether browse V2 sidebar should be shown
  """
  showBrowseV2: Boolean!

  """
  Whether browse v2 is platform mode, which means that platforms are displayed instead of entity types at the root.
  """
  platformBrowseV2: Boolean!

  """
  Whether to show the new lineage visualization.
  """
  lineageGraphV2: Boolean!

  """
  Whether we should show CTAs in the UI related to moving to DataHub Cloud by DataHub.
  """
  showAcrylInfo: Boolean!
  """
  Whether ERModelRelationship Tables Feature should be shown.
  """
  erModelRelationshipFeatureEnabled: Boolean!

  """
  Whether we should show AccessManagement tab in the datahub UI.
  """
  showAccessManagement: Boolean!

  """
  Enables the nested Domains feature that allows users to have sub-Domains.
  If this is off, Domains appear "flat" again.
  """
  nestedDomainsEnabled: Boolean!

  """
  Whether business attribute entity should be shown
  """
  businessAttributeEntityEnabled: Boolean!

  """
  Whether data contracts should be enabled
  """
  dataContractsEnabled: Boolean!

  """
  Whether dataset names are editable
  """
  editableDatasetNameEnabled: Boolean!

  """
  Allows the V2 theme to be turned on.
  Includes new UX for home page, search, entity profiles, and lineage.
  If false, then the V2 experience will be unavailable even if themeV2Default or themeV2Toggleable are true.
  """
  themeV2Enabled: Boolean!

  """
  Sets the default theme to V2.
  If `themeV2Toggleable` is set, then users can toggle between V1 and V2.
  If not, then the default is the only option.
  """
  themeV2Default: Boolean!

  """
  Allows the V2 theme to be toggled by users.
  """
  themeV2Toggleable: Boolean!

  """
  Enables links to schema field-level lineage on lineage explorer.
  """
  schemaFieldCLLEnabled: Boolean!

  """
  If turned on, all siblings will be separated with no way to get to a "combined" sibling view
  """
  showSeparateSiblings: Boolean!

  """
  If turned on, show the manage structured properties tab in the govern dropdown
  """
  showManageStructuredProperties: Boolean!

  """
  If turned on, hides DBT Sources from lineage by:
  i) Hiding the source in the lineage graph, if it has no downstreams
  ii) Swapping to the source's sibling urn on V2 lineage graph
  iii) Representing source sibling as a merged node, with both icons on graph and combined version in sidebar
  """
  hideDbtSourceInLineage: Boolean!

  """
  If turned on, schema field lineage will always fetch ghost entities and present them as real
  """
  schemaFieldLineageIgnoreStatus: Boolean!

  """
  If turned on, show the newly designed nav bar in the V2 experience
  """
  showNavBarRedesign: Boolean!

  """
  If turned on, we display auto complete results. Otherwise, do not.
  """
  showAutoCompleteResults: Boolean!

  """
  If turned on, exposes the versioning feature by allowing users to link entities in the UI.
  """
  entityVersioningEnabled: Boolean!

  """
  If turned on, show the "has siblings" filter in search
  """
  showHasSiblingsFilter: Boolean!

  """
  If turned on, show the redesigned search bar's autocomplete
  """
  showSearchBarAutocompleteRedesign: Boolean!

  """
  If enabled, users will be able to view the tags management experience
  """
  showManageTags: Boolean!

  """
  If enabled, we will show the introduce page in the V2 UI experience to add a title and select platforms
  """
  showIntroducePage: Boolean!

  """
  If turned on, show the re-designed Ingestions page
  """
  showIngestionPageRedesign: Boolean!

  """
  If enabled, show the expand more button (>>) in the lineage graph
  """
  showLineageExpandMore: Boolean!

  """
  If turned on, show the re-designed home page
  """
  showHomePageRedesign: Boolean!

  """
  Enables the redesign of the lineage v2 graph
  """
  lineageGraphV3: Boolean!
}

"""
Input required to update Global View Settings.
"""
input UpdateGlobalViewsSettingsInput {
  """
  The URN of the View that serves as the Global, or organization-wide, default.
  If this field is not provided, the existing Global Default will be cleared.
  """
  defaultView: String
}

"""
Global (platform-level) settings related to the Views feature
"""
type GlobalViewsSettings {
  """
  The global default View. If a user does not have a personal default, then
  this will be the default view.
  """
  defaultView: String
}

"""
Input required to update global applications settings.
"""
input UpdateApplicationsSettingsInput {
  """
  Whether the Applications feature is enabled
  """
  enabled: Boolean
}

"""
Input required to update doc propagation settings.
"""
input UpdateDocPropagationSettingsInput {
  """
  The default doc propagation setting for the platform.
  """
  docColumnPropagation: Boolean
}

"""
Global (platform-level) settings related to the doc propagation feature
"""
type DocPropagationSettings {
  """
  The default doc propagation setting for the platform.
  """
  docColumnPropagation: Boolean
}
"""
Configurations related to DataHub Chrome extension
"""
type ChromeExtensionConfig {
  """
  Whether the Chrome Extension is enabled
  """
  enabled: Boolean!

  """
  Whether lineage is enabled
  """
  lineageEnabled: Boolean!
}

"""
Configuration for any custom theme-ing
"""
type ThemeConfig {
  """
  The optional custom theme ID to determine which theme config we use in the frontend
  """
  themeId: String
}

"""
Global settings related to the home page for an instance
"""
type GlobalHomePageSettings {
  """
  The default page template for the home page for this instance
  """
  defaultTemplate: DataHubPageTemplate
}
