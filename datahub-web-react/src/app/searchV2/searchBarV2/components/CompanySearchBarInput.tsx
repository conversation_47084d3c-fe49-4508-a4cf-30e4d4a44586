import { InputRef } from 'antd';
import { forwardRef, useCallback, useState } from 'react';
import styled from 'styled-components';

import { Icon } from '@components';
import { CommandK } from '@app/searchV2/CommandK';
import { ViewSelect } from '@app/searchV2/searchBarV2/components/ViewSelect';
import { V2_SEARCH_BAR_VIEWS } from '@app/searchV2/constants';

interface CompanySearchBarInputProps {
    value: string;
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
    onSearch?: () => void;
    onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
    onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
    onViewsClick?: (isOpen: boolean) => void;
    onClear?: () => void;
    isDropdownOpened?: boolean;
    placeholder?: string;
    showCommandK?: boolean;
    viewsEnabled?: boolean;
    width?: string;
    isShowNavBarRedesign?: boolean;
}

// Company-branded styled components
const Wrapper = styled.div<{ $open: boolean; $isShowNavBarRedesign?: boolean }>`
    position: relative;
    width: 100%;
`;

const StyledSearchBar = styled.input<{ $isShowNavBarRedesign?: boolean }>`
    width: 100%;
    height: 44px;
    padding: 0 60px 0 50px; /* Space for icons */
    border: 2px solid #e9eaee;
    border-radius: 12px;
    background: white;
    font-size: 16px;
    font-weight: 500;
    color: #004e89; /* Company navy */
    transition: all 0.3s ease;
    outline: none;

    &::placeholder {
        color: #8088a3;
        font-style: italic;
    }

    &:hover {
        border-color: #ffcab0; /* Light orange */
        box-shadow: 0 0 0 3px #fff4f0; /* Very light orange glow */
        transform: translateY(-1px);
    }

    &:focus {
        border-color: #ff6b35; /* Company orange */
        box-shadow: 0 0 0 4px #ffe4d6; /* Light orange focus ring */
        transform: translateY(-1px);
    }

    ${(props) =>
        props.$isShowNavBarRedesign &&
        `
        height: 40px;
        border-radius: 8px;
        font-size: 14px;
    `}
`;

const SearchIcon = styled.div<{ $isShowNavBarRedesign?: boolean }>`
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff6b35; /* Company orange */
    cursor: pointer;
    z-index: 1;

    svg {
        height: 18px;
        width: 18px;
    }

    &:hover {
        color: #e55a2b; /* Darker orange on hover */
    }
`;

const SuffixWrapper = styled.div`
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1;
`;

const ClearIcon = styled.div`
    color: #1a936f; /* Company green */
    cursor: pointer;
    display: flex;
    align-items: center;

    &:hover {
        color: #ff6b35; /* Company orange on hover */
    }

    svg {
        height: 16px;
        width: 16px;
    }
`;

const ViewSelectContainer = styled.div`
    cursor: pointer;
`;

export const CompanySearchBarInput = forwardRef<InputRef, CompanySearchBarInputProps>(
    (
        {
            placeholder,
            onSearch,
            value,
            onFocus,
            onBlur,
            onViewsClick,
            onClear,
            showCommandK,
            isDropdownOpened,
            viewsEnabled,
            width,
            isShowNavBarRedesign,
        },
        ref,
    ) => {
        const [isFocused, setIsFocused] = useState(false);
        const [isViewsSelectOpened, setIsViewsSelectOpened] = useState(false);

        const onFocusHandler = useCallback(
            (event: React.FocusEvent<HTMLInputElement>) => {
                setIsFocused(true);
                onFocus?.(event);
            },
            [onFocus],
        );

        const onBlurHandler = useCallback(
            (event: React.FocusEvent<HTMLInputElement>) => {
                setIsFocused(false);
                onBlur?.(event);
            },
            [onBlur],
        );

        const onKeyDown = useCallback(
            (event: React.KeyboardEvent<HTMLInputElement>) => {
                if (event.key === 'Enter') {
                    onSearch?.();
                }
            },
            [onSearch],
        );

        const onChangeHandler = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
            // Pass the change event to the parent component
            onChange?.(event);
        }, [onChange]);

        const onViewSelectContainerClickHandler = useCallback(
            (event: React.MouseEvent) => {
                event.stopPropagation();
                setIsViewsSelectOpened(!isViewsSelectOpened);
            },
            [isViewsSelectOpened],
        );

        const onViewsClickHandler = useCallback(
            (isOpen: boolean) => {
                setIsViewsSelectOpened(isOpen);
                onViewsClick?.(isOpen);
            },
            [onViewsClick],
        );

        return (
            <Wrapper $open={isDropdownOpened} $isShowNavBarRedesign={isShowNavBarRedesign}>
                <SearchIcon $isShowNavBarRedesign={isShowNavBarRedesign} onClick={onSearch}>
                    <Icon icon="MagnifyingGlass" source="phosphor" weight="fill" />
                </SearchIcon>
                
                <StyledSearchBar
                    placeholder={placeholder}
                    onKeyDown={onKeyDown}
                    value={value}
                    onChange={onChangeHandler}
                    data-testid="company-search-input"
                    onFocus={onFocusHandler}
                    onBlur={onBlurHandler}
                    ref={ref}
                    $isShowNavBarRedesign={isShowNavBarRedesign}
                />
                
                <SuffixWrapper>
                    {(isDropdownOpened || isFocused) && value && (
                        <ClearIcon onClick={onClear}>
                            <Icon icon="XCircle" source="phosphor" size="lg" />
                        </ClearIcon>
                    )}
                    
                    {showCommandK && !isDropdownOpened && !isFocused && <CommandK />}
                    
                    {viewsEnabled && (
                        <ViewSelectContainer
                            onClick={onViewSelectContainerClickHandler}
                            id={V2_SEARCH_BAR_VIEWS}
                        >
                            <ViewSelect isOpen={isViewsSelectOpened} onOpenChange={onViewsClickHandler} />
                        </ViewSelectContainer>
                    )}
                </SuffixWrapper>
            </Wrapper>
        );
    },
);
