import { Skeleton } from 'antd';
import { useCallback } from 'react';
import styled from 'styled-components';

import { SEARCH_BAR_CLASS_NAME } from '@app/searchV2/constants';
import { useSearchContext } from '@app/searchV2/context/SearchContext';
import { useIsShowSeparateSiblingsEnabled } from '@app/useAppConfig';
import { SearchBarV2Props } from '@app/searchV2/searchBarV2/types';
import { StyledAutocomplete } from '@app/searchV2/searchBarV2/components/StyledAutocomplete';
import { SearchBarDropdown } from '@app/searchV2/searchBarV2/components/SearchBarDropdown';
import { AutocompletePlaceholder } from '@app/searchV2/searchBarV2/components/AutocompletePlaceholder';
import { CompanySearchBarInput } from './components/CompanySearchBarInput';

const Wrapper = styled.div`
    width: 100%;
`;

/**
 * Company-branded version of SearchBarV2 with enhanced styling and branding
 */
export const CompanySearchBarV2 = ({
    id,
    isLoading,
    initialQuery,
    placeholderText,
    onSearch,
    fixAutoComplete,
    showCommandK = false,
    viewsEnabled = false,
    combineSiblings = false,
    onFocus,
    onBlur,
    showViewAllResults = false,
    isShowNavBarRedesign,
    width,
}: SearchBarV2Props) => {
    const isShowSeparateSiblingsEnabled = useIsShowSeparateSiblingsEnabled();
    const shouldCombineSiblings = isShowSeparateSiblingsEnabled ? false : combineSiblings;

    const {
        searchQuery,
        isSearching,
        facets,
        appliedFilters,
        hasAppliedFilters,
        hasSelectedView,
        isDropdownVisible,
        options,
        updateFieldFilters,
        clearQueryAndFilters,
        onDropdownVisibilityChangeHandler,
        onSearchHandler,
        onViewsClickHandler,
        onClearFiltersAndSelectedViewHandler,
    } = useSearchContext({
        initialQuery,
        onSearch,
        fixAutoComplete,
        combineSiblings: shouldCombineSiblings,
        showViewAllResults,
    });

    const onClearHandler = useCallback(() => clearQueryAndFilters(), [clearQueryAndFilters]);

    if (isLoading) return <Skeleton />;

    return (
        <Wrapper id={id} className={SEARCH_BAR_CLASS_NAME}>
            <StyledAutocomplete
                dataTestId="company-search-bar"
                defaultActiveFirstOption={false}
                options={options}
                filterOption={false}
                dropdownRender={(menu) => (
                    <SearchBarDropdown
                        menu={menu}
                        query={searchQuery}
                        filters={appliedFilters}
                        updateFilters={updateFieldFilters}
                        facets={facets}
                        isSearching={isSearching}
                    />
                )}
                notFoundContent={
                    <AutocompletePlaceholder
                        hasAppliedFilters={hasAppliedFilters}
                        hasSelectedView={hasSelectedView}
                        isSearching={isSearching}
                        onClearFilters={onClearFiltersAndSelectedViewHandler}
                    />
                }
                onDropdownVisibleChange={onDropdownVisibilityChangeHandler}
                open={isDropdownVisible}
                dropdownContentHeight={480}
                clickOutsideWidth={width === '100%' ? '100%' : undefined}
            >
                <CompanySearchBarInput
                    placeholder={placeholderText || 'Search your company data catalog...'}
                    onSearch={onSearchHandler}
                    value={searchQuery}
                    onFocus={onFocus}
                    onBlur={onBlur}
                    onViewsClick={onViewsClickHandler}
                    onClear={clearQueryAndFilters}
                    showCommandK={showCommandK}
                    isDropdownOpened={isDropdownVisible}
                    viewsEnabled={viewsEnabled}
                    width={width}
                />
            </StyledAutocomplete>
        </Wrapper>
    );
};
