import React from 'react';
import styled from 'styled-components';

import { CompanySearchBarV2 } from './CompanySearchBarV2';

const TestContainer = styled.div`
    padding: 40px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
`;

const Title = styled.h1`
    color: #004e89;
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 10px;
`;

const SearchContainer = styled.div`
    width: 100%;
    max-width: 800px;
    display: flex;
    flex-direction: column;
    gap: 20px;
`;

const FeatureList = styled.div`
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;

    h3 {
        color: #004e89;
        margin-bottom: 20px;
        font-size: 1.5rem;
    }

    ul {
        list-style: none;
        padding: 0;
        color: #5f6685;
        line-height: 2;

        li {
            margin-bottom: 8px;
        }
    }
`;

export const CompanySearchBarTest: React.FC = () => {
    const handleSearch = (query: string) => {
        console.log('Search query:', query);
    };

    return (
        <TestContainer>
            <Title>🎨 Company Search Bar Test</Title>
            
            <SearchContainer>
                <CompanySearchBarV2
                    placeholderText="Search your company data catalog..."
                    onSearch={handleSearch}
                    width="100%"
                    fixAutoComplete
                    viewsEnabled
                    showViewAllResults
                    combineSiblings
                    showCommandK
                />
            </SearchContainer>

            <FeatureList>
                <h3>🌟 Company Branding Features</h3>
                <ul>
                    <li>🎨 Company orange (#FF6B35) and navy (#004E89) colors</li>
                    <li>✨ Smooth hover and focus animations with colored rings</li>
                    <li>🔍 Custom search icon with company orange color</li>
                    <li>🟢 Green clear button for better UX</li>
                    <li>📱 Enhanced responsive design</li>
                    <li>🎯 Custom placeholder text</li>
                    <li>⌨️ Improved keyboard navigation</li>
                </ul>
            </FeatureList>
        </TestContainer>
    );
};
