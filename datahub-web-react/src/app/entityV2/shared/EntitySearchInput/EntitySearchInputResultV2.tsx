import { Icon, Text } from '@components';
import React from 'react';
import styled from 'styled-components';

import { getDisplayedEntityType } from '@app/entityV2/shared/containers/profile/header/utils';
import ContextPath from '@app/previewV2/ContextPath';
import { useEntityRegistry } from '@app/useEntityRegistry';

import { Entity } from '@types';

const Wrapper = styled.div`
    display: flex;
    align-items: center;
    gap: 10px;
`;

const TextWrapper = styled.div`
    display: flex;
    flex-direction: column;
    max-width: 600px;
`;

const IconContainer = styled.img`
    height: 24px;
    min-width: 24px;
`;

type Props = {
    entity: Entity;
};

export default function EntitySearchInputResultV2({ entity }: Props) {
    const entityRegistry = useEntityRegistry();
    const properties = entityRegistry.getGenericEntityProperties(entity.type, entity);
    const platformIcon = properties?.platform?.properties?.logoUrl;

    const displayedEntityType = getDisplayedEntityType(properties, entityRegistry, entity.type);

    return (
        <Wrapper>
            {!platformIcon && <Icon size="4xl" source="phosphor" icon="Placeholder" />}
            {platformIcon && <IconContainer src={platformIcon} />}
            <TextWrapper>
                <Text size="lg">{entityRegistry.getDisplayName(entity.type, entity)}</Text>
                <ContextPath
                    entityType={entity.type}
                    displayedEntityType={displayedEntityType}
                    browsePaths={properties?.browsePathV2}
                    parentEntities={
                        properties?.parentContainers?.containers ||
                        properties?.parentDomains?.domains ||
                        properties?.parentNodes?.nodes
                    }
                    linksDisabled
                />
            </TextWrapper>
        </Wrapper>
    );
}
