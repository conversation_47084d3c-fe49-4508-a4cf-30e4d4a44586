import { foundations } from './foundations';
import { semanticTokens } from './semantic-tokens';

const { colors } = foundations;

// Company-specific semantic tokens that override the defaults
export const companySemanticTokens = {
    colors: {
        ...semanticTokens.colors,
        // Override primary colors with company branding
        primary: colors.companyBrand[500], // Company orange
        secondary: colors.companyBrand.navy, // Company navy
        
        // Custom search-specific tokens
        'search-border': colors.gray[100],
        'search-border-hover': colors.companyBrand[300],
        'search-border-focus': colors.companyBrand[500],
        'search-icon': colors.companyBrand[500],
        'search-text': colors.companyBrand.navy,
        'search-placeholder': colors.gray[1800],
        'search-clear': colors.companyBrand.green,
        
        // Focus ring colors
        'focus-ring-light': colors.companyBrand[0],
        'focus-ring-medium': colors.companyBrand[100],
        
        // Background variations
        'bg-surface-company': colors.companyBrand.lightNavy,
        'bg-accent-company': colors.companyBrand.lightGreen,
    },
};

// Company theme configuration
export const companyTheme = {
    semanticTokens: companySemanticTokens,
    ...foundations,
    
    // Company-specific overrides
    company: {
        name: 'Your Company Name',
        colors: {
            primary: colors.companyBrand[500],
            secondary: colors.companyBrand.navy,
            accent: colors.companyBrand.green,
        },
        branding: {
            logoUrl: '/assets/custom/company-logo.png',
            faviconUrl: '/assets/custom/company-favicon.ico',
            appTitle: 'Your Company Data Catalog',
        },
    },
};

export type CompanyTheme = typeof companyTheme;
export default companyTheme;
