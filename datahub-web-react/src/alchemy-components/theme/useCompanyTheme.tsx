import { useContext, createContext, ReactNode, useState } from 'react';
import { ThemeProvider } from 'styled-components';

import theme from './index';
import companyTheme from './company-theme';

// Theme types
export type ThemeVariant = 'default' | 'company';

// Theme context
interface ThemeContextType {
    currentTheme: ThemeVariant;
    setTheme: (theme: ThemeVariant) => void;
    themeConfig: typeof theme | typeof companyTheme;
}

export const ThemeContext = createContext<ThemeContextType>({
    currentTheme: 'default',
    setTheme: () => {},
    themeConfig: theme,
});

// Hook to use theme
export const useCompanyTheme = () => {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useCompanyTheme must be used within a CompanyThemeProvider');
    }
    return context;
};

// Get theme configuration based on variant
export const getThemeConfig = (variant: ThemeVariant) => {
    switch (variant) {
        case 'company':
            return companyTheme;
        case 'default':
        default:
            return theme;
    }
};

// Theme provider component
interface CompanyThemeProviderProps {
    children: ReactNode;
    initialTheme?: ThemeVariant;
}

export const CompanyThemeProvider = ({ 
    children, 
    initialTheme = 'default' 
}: CompanyThemeProviderProps) => {
    const [currentTheme, setCurrentTheme] = useState<ThemeVariant>(initialTheme);
    const themeConfig = getThemeConfig(currentTheme);

    const contextValue = {
        currentTheme,
        setTheme: setCurrentTheme,
        themeConfig,
    };

    return (
        <ThemeContext.Provider value={contextValue}>
            <ThemeProvider theme={themeConfig}>
                {children}
            </ThemeProvider>
        </ThemeContext.Provider>
    );
};
