import { Input } from 'antd';
import styled from 'styled-components';

import { colors, typography } from '@src/alchemy-components/theme';

export const StyledSearchBar = styled(Input)<{ $width?: string; $height?: string }>`
    height: ${(props) => props.$height};
    width: ${(props) => props.$width};
    display: flex;
    align-items: center;
    border-radius: 12px; /* Increased border radius for modern look */
    border: 2px solid ${colors.gray[100]}; /* Thicker border */
    background: ${colors.white};
    transition: all 0.3s ease; /* Smooth transitions */

    input {
        color: ${colors.companyBrand.navy}; /* Company navy for text */
        font-size: ${typography.fontSizes.md} !important;
        font-weight: 500; /* Slightly bolder text */

        &::placeholder {
            color: ${colors.gray[1800]};
            font-style: italic;
        }
    }

    .ant-input-prefix {
        width: 20px;
        color: ${colors.companyBrand[500]}; /* Company orange for search icon */
        margin-right: 8px;

        svg {
            height: 18px; /* Slightly larger icon */
            width: 18px;
        }
    }

    /* Custom hover and focus states with company branding */
    &:hover {
        border-color: ${colors.companyBrand[300]} !important;
        box-shadow: 0 0 0 3px ${colors.companyBrand[0]} !important; /* Light orange glow */
        transform: translateY(-1px); /* Subtle lift effect */
    }

    &:focus,
    &:focus-within {
        border-color: ${colors.companyBrand[500]} !important; /* Company orange border */
        box-shadow: 0 0 0 4px ${colors.companyBrand[100]} !important; /* Orange focus ring */
        transform: translateY(-1px);
    }

    /* Custom clear button styling */
    .ant-input-clear-icon {
        color: ${colors.companyBrand.green}; /* Company green for clear button */

        &:hover {
            color: ${colors.companyBrand[500]};
        }
    }
`;
