import { InputProps, InputRef } from 'antd';
import { forwardRef } from 'react';
import styled from 'styled-components';

import { SearchBarProps } from '@components/components/SearchBar/types';
import { useCompanyTheme } from '@src/alchemy-components/theme/useCompanyTheme';

import { Icon } from '@src/alchemy-components';

// Enhanced styled search bar with company theming
const CompanyStyledSearchBar = styled.input<{ $width?: string; $height?: string }>`
    height: ${(props) => props.$height};
    width: ${(props) => props.$width};
    display: flex;
    align-items: center;
    border-radius: 12px;
    border: 2px solid ${({ theme }) => theme.semanticTokens.colors['search-border']};
    background: ${({ theme }) => theme.semanticTokens.colors['body-bg']};
    padding: 0 16px 0 48px; /* Space for search icon */
    font-size: 16px;
    font-weight: 500;
    color: ${({ theme }) => theme.semanticTokens.colors['search-text']};
    transition: all 0.3s ease;
    position: relative;

    &::placeholder {
        color: ${({ theme }) => theme.semanticTokens.colors['search-placeholder']};
        font-style: italic;
    }

    &:hover {
        border-color: ${({ theme }) => theme.semanticTokens.colors['search-border-hover']};
        box-shadow: 0 0 0 3px ${({ theme }) => theme.semanticTokens.colors['focus-ring-light']};
        transform: translateY(-1px);
    }

    &:focus {
        outline: none;
        border-color: ${({ theme }) => theme.semanticTokens.colors['search-border-focus']};
        box-shadow: 0 0 0 4px ${({ theme }) => theme.semanticTokens.colors['focus-ring-medium']};
        transform: translateY(-1px);
    }
`;

const SearchContainer = styled.div<{ $width?: string; $height?: string }>`
    position: relative;
    width: ${(props) => props.$width};
    height: ${(props) => props.$height};
`;

const SearchIcon = styled.div`
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: ${({ theme }) => theme.semanticTokens.colors['search-icon']};
    z-index: 1;
    pointer-events: none;

    svg {
        height: 18px;
        width: 18px;
    }
`;

const ClearButton = styled.button<{ $visible: boolean }>`
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: ${({ theme }) => theme.semanticTokens.colors['search-clear']};
    cursor: pointer;
    opacity: ${(props) => (props.$visible ? 1 : 0)};
    transition: opacity 0.2s ease;
    z-index: 1;

    &:hover {
        color: ${({ theme }) => theme.semanticTokens.colors.primary};
    }

    svg {
        height: 16px;
        width: 16px;
    }
`;

// Company branding defaults
export const companySearchBarDefaults: SearchBarProps = {
    placeholder: 'Search your company data catalog...',
    value: '',
    width: '100%',
    height: '44px',
    allowClear: true,
};

export const CompanySearchBar = forwardRef<InputRef, SearchBarProps & Omit<InputProps, 'onChange'>>(
    (
        {
            placeholder = companySearchBarDefaults.placeholder,
            value = companySearchBarDefaults.value,
            width = companySearchBarDefaults.width,
            height = companySearchBarDefaults.height,
            allowClear = companySearchBarDefaults.allowClear,
            onChange,
            ...props
        },
        ref,
    ) => {
        const { themeConfig } = useCompanyTheme();
        const hasValue = Boolean(value);

        const handleClear = () => {
            if (onChange) {
                onChange('', {} as any);
            }
        };

        return (
            <SearchContainer $width={width} $height={height}>
                <SearchIcon>
                    <Icon icon="MagnifyingGlass" source="phosphor" weight="fill" />
                </SearchIcon>
                <CompanyStyledSearchBar
                    placeholder={placeholder}
                    onChange={(e) => onChange?.(e.target.value, e)}
                    value={value}
                    $width={width}
                    $height={height}
                    data-testid="company-search-bar-input"
                    ref={ref as any}
                    theme={themeConfig}
                    {...props}
                />
                {allowClear && hasValue && (
                    <ClearButton $visible={hasValue} onClick={handleClear} type="button">
                        <Icon icon="X" source="phosphor" weight="bold" />
                    </ClearButton>
                )}
            </SearchContainer>
        );
    },
);
