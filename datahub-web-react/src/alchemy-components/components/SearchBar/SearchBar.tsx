import { InputProps, InputRef } from 'antd';
import { forwardRef } from 'react';

import { StyledSearchBar } from '@components/components/SearchBar/components';
import { SearchBarProps } from '@components/components/SearchBar/types';

import { Icon } from '@src/alchemy-components';

// Company branding defaults
export const searchBarDefaults: SearchBarProps = {
    placeholder: 'Search your company data...',
    value: '',
    width: '100%',
    height: '44px', // Slightly taller search bar
    allowClear: true,
};

export const SearchBar = forwardRef<InputRef, SearchBarProps & Omit<InputProps, 'onChange'>>(
    (
        {
            placeholder = searchBarDefaults.placeholder,
            value = searchBarDefaults.value,
            width = searchBarDefaults.width,
            height = searchBarDefaults.height,
            allowClear = searchBarDefaults.allowClear,
            clearIcon,
            onChange,
            ...props
        },
        ref,
    ) => {
        // Custom clear icon with company branding
        const customClearIcon = clearIcon || <Icon icon="X" source="phosphor" weight="bold" />;

        return (
            <StyledSearchBar
                placeholder={placeholder}
                onChange={(e) => onChange?.(e.target.value, e)}
                value={value}
                prefix={<Icon icon="MagnifyingGlass" source="phosphor" weight="fill" />}
                allowClear={allowClear && { clearIcon: customClearIcon }}
                $width={width}
                $height={height}
                data-testid="search-bar-input"
                ref={ref}
                {...props}
            />
        );
    },
);
