import React, { useState } from 'react';
import styled from 'styled-components';

import { CompanyThemeProvider } from '@src/alchemy-components/theme/useCompanyTheme';
import { CompanySearchBar } from './CompanySearchBar';

const ExampleContainer = styled.div`
    padding: 40px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
`;

const Title = styled.h1`
    color: #004e89;
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 10px;
`;

const Subtitle = styled.p`
    color: #5f6685;
    font-size: 1.2rem;
    text-align: center;
    max-width: 600px;
    line-height: 1.6;
`;

const SearchContainer = styled.div`
    width: 100%;
    max-width: 600px;
    display: flex;
    flex-direction: column;
    gap: 20px;
`;

const ThemeToggle = styled.button`
    padding: 12px 24px;
    background: #ff6b35;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        background: #e55a2b;
        transform: translateY(-2px);
    }
`;

const SearchResults = styled.div`
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #5f6685;
    font-style: italic;
`;

export const CompanySearchBarExample: React.FC = () => {
    const [searchValue, setSearchValue] = useState('');
    const [currentTheme, setCurrentTheme] = useState<'default' | 'company'>('company');

    const handleSearchChange = (value: string) => {
        setSearchValue(value);
    };

    const toggleTheme = () => {
        setCurrentTheme(prev => prev === 'default' ? 'company' : 'default');
    };

    return (
        <CompanyThemeProvider initialTheme={currentTheme}>
            <ExampleContainer>
                <Title>🎨 Company Branded Search</Title>
                <Subtitle>
                    Experience our custom-branded search component with company colors, 
                    enhanced interactions, and modern design elements.
                </Subtitle>
                
                <ThemeToggle onClick={toggleTheme}>
                    Switch to {currentTheme === 'default' ? 'Company' : 'Default'} Theme
                </ThemeToggle>

                <SearchContainer>
                    <CompanySearchBar
                        value={searchValue}
                        onChange={handleSearchChange}
                        placeholder="Search datasets, dashboards, users, and more..."
                        width="100%"
                        height="50px"
                    />
                    
                    <SearchResults>
                        {searchValue ? (
                            <div>
                                <strong>Searching for:</strong> "{searchValue}"
                                <br />
                                <small>Results would appear here in a real implementation</small>
                            </div>
                        ) : (
                            'Start typing to see search results...'
                        )}
                    </SearchResults>
                </SearchContainer>

                <div style={{ marginTop: '40px', textAlign: 'center' }}>
                    <h3 style={{ color: '#004e89', marginBottom: '15px' }}>
                        🌟 Custom Features
                    </h3>
                    <ul style={{ 
                        listStyle: 'none', 
                        padding: 0, 
                        color: '#5f6685',
                        lineHeight: '1.8'
                    }}>
                        <li>🎨 Company brand colors (Orange #FF6B35, Navy #004E89)</li>
                        <li>✨ Smooth hover and focus animations</li>
                        <li>🔍 Custom search and clear icons</li>
                        <li>📱 Enhanced accessibility and responsiveness</li>
                        <li>🎯 Branded placeholder text</li>
                    </ul>
                </div>
            </ExampleContainer>
        </CompanyThemeProvider>
    );
};
