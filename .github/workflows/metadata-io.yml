name: metadata-io
on:
  push:
    branches:
      - master
      - releases/**
    paths:
      - "**/*.gradle"
      - "li-utils/**"
      - "metadata-models/**"
      - "metadata-io/**"
      - ".github/workflows/metadata-io.yml"
  pull_request:
    branches:
      - "**"
    paths:
      - "**/*.gradle"
      - "li-utils/**"
      - "metadata-models/**"
      - "metadata-io/**"
      - ".github/workflows/metadata-io.yml"
  release:
    types: [published]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      frontend_change: ${{ steps.ci-optimize.outputs.frontend-change == 'true' || github.event_name == 'release' }}
      ingestion_change: ${{ steps.ci-optimize.outputs.ingestion-change == 'true' || github.event_name == 'release' }}
      backend_change: ${{ steps.ci-optimize.outputs.backend-change == 'true' || github.event_name == 'release' }}
      docker_change: ${{ steps.ci-optimize.outputs.docker-change == 'true' }}
      frontend_only: ${{ steps.ci-optimize.outputs.frontend-only == 'true' }}
      ingestion_only: ${{ steps.ci-optimize.outputs.ingestion-only == 'true' }}
      kafka_setup_change: ${{ steps.ci-optimize.outputs.kafka-setup-change == 'true' }}
      mysql_setup_change: ${{ steps.ci-optimize.outputs.mysql-setup-change == 'true' }}
      postgres_setup_change: ${{ steps.ci-optimize.outputs.postgres-setup-change == 'true' }}
      elasticsearch_setup_change: ${{ steps.ci-optimize.outputs.elasticsearch-setup-change == 'true' }}
    steps:
      - name: Check out the repo
        uses: acryldata/sane-checkout-action@v3
      - uses: ./.github/actions/ci-optimization
        id: ci-optimize
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    needs: setup
    steps:
      - name: Free up disk space
        run: |
          sudo apt-get remove 'dotnet-*' azure-cli || true
          sudo rm -rf /usr/local/lib/android/ || true
          sudo docker image prune -a -f || true
      - name: Disk Check
        run: df -h . && docker images
      - uses: acryldata/sane-checkout-action@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: "zulu"
          java-version: 17
      - uses: gradle/actions/setup-gradle@v4
      - name: Gradle build (and test)
        run: |
          ./gradlew :metadata-io:test
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: Test Results (metadata-io)
          path: |
            **/build/reports/tests/test/**
            **/build/test-results/test/**
            **/junit.*.xml
            !**/binary/**
      - name: Ensure codegen is updated
        uses: ./.github/actions/ensure-codegen-updated
      - name: Upload coverage to Codecov
        if: ${{ always()}}
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./build/coverage-reports/metadata-io/
          fail_ci_if_error: false
          flags: metadata-io
          name: metadata-io-test
          verbose: true
          override_branch: ${{ github.head_ref || github.ref_name }}
      - name: Upload test results to Codecov
        if: ${{ !cancelled() }}
        uses: codecov/test-results-action@v1
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          override_branch: ${{ github.head_ref || github.ref_name }}

  event-file:
    runs-on: ubuntu-latest
    steps:
      - name: Upload
        uses: actions/upload-artifact@v4
        with:
          name: Event File
          path: ${{ github.event_path }}
