name: metadata ingestion
on:
  push:
    branches:
      - master
      - releases/**
    paths:
      - ".github/workflows/metadata-ingestion.yml"
      - "metadata-ingestion/**"
      - "metadata-ingestion-modules/**"
      - "metadata-models/**"
  pull_request:
    branches:
      - "**"
    paths:
      - ".github/workflows/metadata-ingestion.yml"
      - "metadata-ingestion/**"
      - "metadata-ingestion-modules/**"
      - "metadata-models/**"
  release:
    types: [published]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  metadata-ingestion:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    env:
      DATAHUB_TELEMETRY_ENABLED: false
      # TODO: Enable this once the test is fixed.
      # DATAHUB_LOOKML_GIT_TEST_SSH_KEY: ${{ secrets.DATAHUB_LOOKML_GIT_TEST_SSH_KEY }}
    strategy:
      matrix:
        python-version: ["3.9", "3.11"]
        command:
          [
            "testQuick",
            "testIntegrationBatch0",
            "testIntegrationBatch1",
            "testIntegrationBatch2",
          ]
      fail-fast: false
    steps:
      - name: Free up disk space
        run: |
          sudo apt-get remove 'dotnet-*' azure-cli || true
          sudo rm -rf /usr/local/lib/android/ || true
          sudo docker image prune -a -f || true
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: "zulu"
          java-version: 17
      - uses: gradle/actions/setup-gradle@v4
      - uses: acryldata/sane-checkout-action@v3
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/uv
          key: ${{ runner.os }}-uv-${{ hashFiles('**/requirements.txt') }}
      - name: Install dependencies
        run: ./metadata-ingestion/scripts/install_deps.sh
      - name: Install package
        run: ./gradlew :metadata-ingestion:installPackageOnly
      - name: Check lint passes and autogenerated JSON files are up-to-date
        if: ${{  matrix.command == 'testQuick' }}
        run: |
          ./gradlew :metadata-ingestion:lint
      - name: Check autogenerated JSON files are up-to-date
        if: ${{  matrix.command == 'testQuick' }}
        run: |
          ./gradlew :metadata-ingestion:capabilitySummary :metadata-ingestion:lineageGen
          for json_file in metadata-ingestion/src/datahub/ingestion/autogenerated/*.json; do
            filename=$(basename "$json_file")
            if git diff --quiet "$json_file"; then
              echo "✅ $filename is unchanged"
            else
              echo "❌ $filename has changed. Please commit the updated file."
              echo "Changed lines:"
              git diff "$json_file"
              exit 1
            fi
          done
      - name: Run metadata-ingestion tests
        run: ./gradlew :metadata-ingestion:${{ matrix.command }}
      - name: Debug info
        if: always()
        run: |
          source metadata-ingestion/venv/bin/activate && uv pip freeze
          set -x
          df -hl
          docker image ls
          docker system df
      - uses: actions/upload-artifact@v4
        with:
          name: Test Results (metadata ingestion ${{ matrix.python-version }} ${{ matrix.command }})
          path: |
            **/build/reports/tests/test/**
            **/build/test-results/test/**
            **/junit.*.xml
            !**/binary/**
      - name: Upload coverage to Codecov with ingestion flag
        if: ${{ always() && matrix.python-version == '3.11' }}
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./build/coverage-reports/metadata-ingestion/
          fail_ci_if_error: false
          flags: ingestion
          name: pytest-${{ matrix.python-version }}-${{ matrix.command }}
          verbose: true
          override_branch: ${{ github.head_ref || github.ref_name }}
      - name: Upload test results to Codecov
        if: ${{ !cancelled() }}
        uses: codecov/test-results-action@v1
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          override_branch: ${{ github.head_ref || github.ref_name }}

  event-file:
    runs-on: ubuntu-latest
    steps:
      - name: Upload
        uses: actions/upload-artifact@v4
        with:
          name: Event File
          path: ${{ github.event_path }}
