name: documentation

on:
  pull_request:
    branches:
      - "**"
    paths:
      - ".github/workflows/documentation.yml"
      - "metadata-ingestion/**"
      - "metadata-models/**"
      - "docs/**"
      - "docs-website/**"
  push:
    branches:
      - master
    paths:
      - ".github/workflows/documentation.yml"
      - "metadata-ingestion/**"
      - "metadata-models/**"
      - "docs/**"
      - "docs-website/**"
  # release:
  #   types: [published, edited]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  gh-pages:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      # We explicitly don't use acryldata/sane-checkout-action because docusaurus runs
      # git commands to determine the last change date for each file.
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: "zulu"
          java-version: 17
      - uses: gradle/actions/setup-gradle@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/uv
          key: ${{ runner.os }}-uv-${{ hashFiles('**/requirements.txt') }}
      - name: Install Python dependencies
        run: ./metadata-ingestion/scripts/install_deps.sh
      - name: Run tests
        run: |
          ./gradlew --info :metadata-ingestion:testScripts
      - name: Build Docs
        run: |
          ./gradlew --info docs-website:build

      - name: Deploy
        if: github.event_name == 'push' && github.repository == 'datahub-project/datahub'
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs-website/build
          cname: datahubproject.io
          # The gh-pages branch stores the built docs site. We don't need to preserve
          # the full history of the .html files, since they're generated from our
          # source files. Doing so significantly reduces the size of the repo's .git dir.
          force_orphan: true
