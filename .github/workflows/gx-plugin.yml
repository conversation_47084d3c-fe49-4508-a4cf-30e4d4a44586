name: GX Plugin
on:
  push:
    branches:
      - master
      - releases/**
    paths:
      - ".github/workflows/gx-plugin.yml"
      - "metadata-ingestion-modules/gx-plugin/**"
      - "metadata-ingestion/**"
      - "metadata-models/**"
  pull_request:
    branches:
      - master
    paths:
      - ".github/**"
      - "metadata-ingestion-modules/gx-plugin/**"
      - "metadata-ingestion/**"
      - "metadata-models/**"
  release:
    types: [published]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  gx-plugin:
    runs-on: ubuntu-latest
    env:
      DATAHUB_TELEMETRY_ENABLED: false
    strategy:
      matrix:
        python-version: ["3.9", "3.10"]
        include:
          - python-version: "3.9"
            extraPythonRequirement: "great-expectations~=0.15.12"
          - python-version: "3.10"
            extraPythonRequirement: "great-expectations~=0.16.0 numpy~=1.26.0"
          - python-version: "3.11"
            extraPythonRequirement: "great-expectations~=0.17.0"
          - python-version: "3.11"
            extraPythonRequirement: "great-expectations~=0.18.0"
      fail-fast: false
    steps:
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: "zulu"
          java-version: 17
      - uses: gradle/actions/setup-gradle@v4
      - uses: acryldata/sane-checkout-action@v3
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          cache: "pip"
      - name: Install dependencies
        run: ./metadata-ingestion/scripts/install_deps.sh
      - name: Install GX package and test  (extras ${{ matrix.extraPythonRequirement }})
        run: ./gradlew -Pextra_pip_requirements='${{ matrix.extraPythonRequirement }}' :metadata-ingestion-modules:gx-plugin:lint :metadata-ingestion-modules:gx-plugin:testQuick
      - name: pip freeze show list installed
        if: always()
        run: source metadata-ingestion-modules/gx-plugin/venv/bin/activate && uv pip freeze
      - uses: actions/upload-artifact@v4
        if: ${{ always() && matrix.python-version == '3.11' && matrix.extraPythonRequirement == 'great-expectations~=0.17.0' }}
        with:
          name: Test Results (GX Plugin ${{ matrix.python-version}})
          path: |
            **/build/reports/tests/test/**
            **/build/test-results/test/**
            **/junit.*.xml
      - name: Upload coverage to Codecov with ingestion flag
        if: always()
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./build/coverage-reports/metadata-ingestion-modules/gx-plugin/
          fail_ci_if_error: false
          flags: ingestion-gx-plugin
          name: pytest-gx
          verbose: true
          override_branch: ${{ github.head_ref || github.ref_name }}
      - name: Upload test results to Codecov
        if: ${{ !cancelled() }}
        uses: codecov/test-results-action@v1
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          override_branch: ${{ github.head_ref || github.ref_name }}

  event-file:
    runs-on: ubuntu-latest
    steps:
      - name: Upload
        uses: actions/upload-artifact@v4
        with:
          name: Event File
          path: ${{ github.event_path }}
