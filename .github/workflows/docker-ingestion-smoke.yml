name: ingestion smoke
on:
  release:
    types: [published]
  push:
    paths:
      - "docker/datahub-ingestion-base/**"
      - "smoke-test/**"
      - ".github/workflows/docker-ingestion-smoke.yml"
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  IMAGE: acryldata/datahub-ingestion-smoke

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      tag: ${{ steps.tag.outputs.tag }}
      publish: ${{ steps.publish.outputs.publish }}
      python_release_version: ${{ steps.python_release_version.outputs.release_version }}
    steps:
      - name: Checkout
        uses: acryldata/sane-checkout-action@v3
      - name: Compute Tag
        id: tag
        env:
          GITHUB_REF_FALLBACK: ${{ github.ref }}
          GITHUB_EVENT_NAME: ${{ github.event_name }}
        run: |
          source .github/scripts/docker_helpers.sh
          echo "tag=$(get_tag)" >> $GITHUB_OUTPUT
      - name: Compute Python Release Version
        id: python_release_version
        run: |
          source .github/scripts/docker_helpers.sh
          echo "release_version=$(get_python_docker_release_v)" >> $GITHUB_OUTPUT
      - name: Check whether publishing enabled
        id: publish
        env:
          ENABLE_PUBLISH: ${{ secrets.ACRYL_DOCKER_PASSWORD }}
        run: |
          echo "Enable publish: ${{ env.ENABLE_PUBLISH != '' }}"
          echo "publish=${{ env.ENABLE_PUBLISH != '' }}" >> $GITHUB_OUTPUT
  build-smoke:
    name: Build and Push Docker Image to Docker Hub
    runs-on: ubuntu-latest
    needs: setup
    if: ${{ needs.setup.outputs.publish == 'true' }}
    steps:
      - name: Check out the repo
        uses: acryldata/sane-checkout-action@v3
      - name: Build and push
        uses: ./.github/actions/docker-custom-build-and-push
        with:
          images: |
            ${{ env.IMAGE }}
          tags: ${{ needs.setup.outputs.tag }}
          username: ${{ secrets.ACRYL_DOCKER_USERNAME }}
          password: ${{ secrets.ACRYL_DOCKER_PASSWORD }}
          publish: ${{ needs.setup.outputs.publish == 'true' }}
          context: .
          file: ./docker/datahub-ingestion-base/smoke.Dockerfile
          platforms: linux/amd64,linux/arm64/v8
          build-args: |
            RELEASE_VERSION=${{ needs.setup.outputs.python_release_version }}
