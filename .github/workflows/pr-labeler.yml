name: "Pull Request Labeler"
on:
  pull_request_target:
    types: [opened, reopened]

permissions:
  contents: read
  pull-requests: write

jobs:
  triage:
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/labeler@v5
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          configuration-path: ".github/pr-labeler-config.yml"
      - uses: actions-ecosystem/action-add-labels@v1.1.3
        # only add names of DataHub team members here
        if: ${{
          !contains(
          fromJson('[
          "anshbansal",
          "asikowitz",
          "chriscollins3456",
          "david-leifker",
          "shirshanka",
          "swaroopjagadish",
          "treff7es",
          "yoonhyejin",
          "gabe-lyons",
          "hsheth2",
          "jjoyce0510",
          "maggiehays",
          "pedro93",
          "RyanHolstien",
          "sakethvarma397",
          "purnimagarg1",
          "sagar-salvi-apptware",
          "kushagra-apptware",
          "Salman-Apptware",
          "mayurinehate",
          "noggi",
          "skrydal",
          "kevinkarchacryl",
          "sgomezvillamor",
          "acrylJonny",
          "chakru-r",
          "brock-acryl",
          "mminichino",
          "jayacryl",
          "v-tarasevich-blitz-brain",
          "ryota-cloud",
          "annadoesdesign",
          "jmacryl",
          "esteban",
          "anthonyburdi",
          "ligfx",
          "ksrinath"
          ]'),
          github.actor
          )
          }}
        with:
          github_token: ${{ github.token }}
          labels: |
            community-contribution
      - uses: actions-ecosystem/action-add-labels@v1.1.3
        # only add names of champions here. Confirm with DevRel Team
        if: ${{
          contains(
          fromJson('[
          "siladitya2",
          "bossenti",
          "PatrickfBraz",
          "cuong-pham",
          "sudhakarast",
          "tkdrahn",
          "rtekal",
          "mikeburke24",
          "DSchmidtDev"
          ]'),
          github.actor
          )
          }}
        with:
          github_token: ${{ github.token }}
          labels: |
            datahub-community-champion
